/**
 * Script para corrigir timestamps em broadcasts existentes
 * 
 * Este script adiciona os campos createdAt e updatedAt aos documentos
 * da coleção shotx-cron que não possuem esses campos, usando a data
 * de scheduled_date como referência para createdAt.
 * 
 * Uso: node src/scripts/fix-broadcast-timestamps.js
 */

import { FirestoreRef } from '../firebase/index.js';
import moment from 'moment';

const SHOTX_CRON_COLLECTION_NAME = 'shotx-cron';

/**
 * Função principal para corrigir timestamps
 */
async function fixBroadcastTimestamps() {
    console.log('🚀 Iniciando correção de timestamps em broadcasts...');
    
    try {
        // Buscar todos os documentos da coleção shotx-cron
        const snapshot = await FirestoreRef.collection(SHOTX_CRON_COLLECTION_NAME).get();
        
        if (snapshot.empty) {
            console.log('📭 Nenhum broadcast encontrado na coleção.');
            return;
        }
        
        console.log(`📊 Encontrados ${snapshot.size} broadcasts para verificar.`);
        
        let updatedCount = 0;
        let skippedCount = 0;
        const batch = FirestoreRef.batch();
        
        snapshot.forEach((doc) => {
            const data = doc.data();
            const docRef = doc.ref;
            
            // Verificar se já possui os campos necessários
            const hasCreatedAt = data.createdAt && typeof data.createdAt === 'number';
            const hasUpdatedAt = data.updatedAt && typeof data.updatedAt === 'number';
            
            if (hasCreatedAt && hasUpdatedAt) {
                skippedCount++;
                return; // Pular documentos que já possuem os campos
            }
            
            // Preparar dados para atualização
            const updateData = {};
            
            if (!hasCreatedAt) {
                // Usar scheduled_date como referência para createdAt
                let createdAtTimestamp;
                
                if (data.scheduled_date) {
                    // Converter scheduled_date para timestamp Unix
                    const scheduledMoment = moment(data.scheduled_date);
                    if (scheduledMoment.isValid()) {
                        createdAtTimestamp = scheduledMoment.unix();
                    } else {
                        // Fallback: usar timestamp atual
                        createdAtTimestamp = moment().unix();
                    }
                } else {
                    // Fallback: usar timestamp atual
                    createdAtTimestamp = moment().unix();
                }
                
                updateData.createdAt = createdAtTimestamp;
            }
            
            if (!hasUpdatedAt) {
                // Usar createdAt como base para updatedAt, ou timestamp atual
                updateData.updatedAt = updateData.createdAt || data.createdAt || moment().unix();
            }
            
            // Adicionar ao batch
            batch.update(docRef, updateData);
            updatedCount++;
            
            console.log(`📝 Preparando atualização para broadcast ${doc.id}:`, {
                scheduled_date: data.scheduled_date,
                createdAt: updateData.createdAt ? moment.unix(updateData.createdAt).format('YYYY-MM-DD HH:mm:ss') : 'já existe',
                updatedAt: updateData.updatedAt ? moment.unix(updateData.updatedAt).format('YYYY-MM-DD HH:mm:ss') : 'já existe'
            });
        });
        
        if (updatedCount > 0) {
            console.log(`💾 Executando atualização em lote para ${updatedCount} broadcasts...`);
            await batch.commit();
            console.log('✅ Atualização concluída com sucesso!');
        } else {
            console.log('ℹ️ Nenhum broadcast precisou ser atualizado.');
        }
        
        console.log(`📈 Resumo da operação:`);
        console.log(`   - Broadcasts atualizados: ${updatedCount}`);
        console.log(`   - Broadcasts ignorados (já tinham timestamps): ${skippedCount}`);
        console.log(`   - Total processado: ${snapshot.size}`);
        
    } catch (error) {
        console.error('❌ Erro ao corrigir timestamps:', error);
        throw error;
    }
}

/**
 * Função para verificar o status dos timestamps
 */
async function checkTimestampStatus() {
    console.log('🔍 Verificando status dos timestamps...');
    
    try {
        const snapshot = await FirestoreRef.collection(SHOTX_CRON_COLLECTION_NAME).get();
        
        if (snapshot.empty) {
            console.log('📭 Nenhum broadcast encontrado.');
            return;
        }
        
        let withCreatedAt = 0;
        let withUpdatedAt = 0;
        let withBothFields = 0;
        let withoutAnyField = 0;
        
        snapshot.forEach((doc) => {
            const data = doc.data();
            const hasCreatedAt = data.createdAt && typeof data.createdAt === 'number';
            const hasUpdatedAt = data.updatedAt && typeof data.updatedAt === 'number';
            
            if (hasCreatedAt) withCreatedAt++;
            if (hasUpdatedAt) withUpdatedAt++;
            if (hasCreatedAt && hasUpdatedAt) withBothFields++;
            if (!hasCreatedAt && !hasUpdatedAt) withoutAnyField++;
        });
        
        console.log(`📊 Status dos timestamps:`);
        console.log(`   - Total de broadcasts: ${snapshot.size}`);
        console.log(`   - Com createdAt: ${withCreatedAt}`);
        console.log(`   - Com updatedAt: ${withUpdatedAt}`);
        console.log(`   - Com ambos os campos: ${withBothFields}`);
        console.log(`   - Sem nenhum campo: ${withoutAnyField}`);
        
    } catch (error) {
        console.error('❌ Erro ao verificar status:', error);
        throw error;
    }
}

// Executar o script
async function main() {
    const args = process.argv.slice(2);
    const command = args[0] || 'fix';
    
    switch (command) {
        case 'check':
            await checkTimestampStatus();
            break;
        case 'fix':
        default:
            await checkTimestampStatus();
            console.log('\n' + '='.repeat(50) + '\n');
            await fixBroadcastTimestamps();
            console.log('\n' + '='.repeat(50) + '\n');
            await checkTimestampStatus();
            break;
    }
    
    console.log('🎉 Script concluído!');
}

// Executar apenas se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch((error) => {
        console.error('💥 Erro fatal:', error);
        process.exit(1);
    });
}

export { fixBroadcastTimestamps, checkTimestampStatus };
