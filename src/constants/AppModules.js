// initialize helpers
const { ThemeColors } = require('./ThemeColors')
const roles = require('./UsersRoles')
const COLLECTIONS = require('./AppCollections')
const LanguageModules = require('./LanguageModules').default

import { SNIPER_URL } from './AppConstants'

const accountModel = 'Routes/accounts/model'
const calendarModel = 'Routes/calendar/model'
const tasklistsModel = 'Routes/tasklists/model'
const eventsModel = 'Routes/events/model'
const campaignsModel = 'Routes/campaigns/model'
const storesModel = 'Routes/stores/model'
const productsModel = 'Routes/products/model'
const barcodeProductsModel = null
const productsListsModel = null
const packsModel = null
const mailingModel = 'Routes/mailing/model'
const leadsModel = 'Routes/leads/model'
const funnelsModel = 'Routes/funnels/model'
const dealsModel = 'Routes/deals/model'
const automationsModel = 'Routes/automations/model'
const formsModel = 'Routes/forms/model'
const fieldsModel = null
const questionnairesModel = 'Routes/questionnaires/model'
const landingPagesModel = 'Routes/landing-pages/model'
const checkinModel = null
const participantsModel = 'Models/childnodes/participant'
const segmentationsModel = 'Routes/segmentations/model'
const qiusersModel = 'Routes/qiusers/model'
const affiliatesModel = 'Routes/affiliates/model'
const teamsModel = 'Routes/teams/model'
const notificationsModel = 'Routes/notifications/model'
const livePostsModel = null
const hotPostsModel = null
const prizesModel = null
const qiplusPlansModel = null
const qipluszapModel = null
const rafflesModel = null
const sorteioEventoModel = null
const tagsModel = null
const templatesModel = null
const ticketsModel = 'Routes/tickets/model'
const contractsModel = 'Routes/contracts/model'
const trackingsModel = 'Routes/trackings/model'
const integrationsModel = 'Routes/integrations/model'

const {
  WEBMASTER_ROLE,
  ADMIN_ROLE,
  OWNER_ROLE,
  MANAGER_ROLE,
  SELLER_ROLE,
  OPERATOR_ROLE,
  WEBMASTER_LEVEL,
  ADMIN_LEVEL,
  OWNER_LEVEL,
  MANAGER_LEVEL,
  SELLER_LEVEL,
  OPERATOR_LEVEL,
} = roles

/**
 * App Modules
 */
const userLocale = localStorage.getItem('config.locale') || navigator?.language?.split('-')?.[0] || 'en'
const lang = ['pt', 'en', 'es'].includes(userLocale) ? userLocale : 'en'

// Debug
console.log('LanguageModules:', LanguageModules);
console.log('Current language:', lang);

// Use a try-catch block to handle potential errors
let langModules;
try {
  langModules = LanguageModules[lang] || LanguageModules.en;
  console.log('Language modules loaded:', langModules ? 'Yes' : 'No');
} catch (error) {
  console.error('Error loading language modules:', error);
  langModules = {
    calendar: { label: 'Calendar', singular: 'Event', shortname: 'Event' },
    tasklists: { label: 'Checklists', singular: 'Checklist', shortname: 'Checklist' },
    events: { label: 'Events', singular: 'Event', shortname: 'Event' },
    campaigns: { label: 'Campaigns', singular: 'Campaign', shortname: 'Campaign' },
    stores: { label: 'Stores', singular: 'Store', shortname: 'Store' },
    products: { label: 'Products', singular: 'Product', shortname: 'Product' },
    leads: { label: 'Leads', singular: 'Lead', shortname: 'Lead' },
    deals: { label: 'Deals', singular: 'Deal', shortname: 'Deal' },
    funnels: { label: 'Sales Funnels', singular: 'Sales Funnel', shortname: 'Funnel' },
    automations: { label: 'Automations', singular: 'Automation', shortname: 'Automation' },
    forms: { label: 'Forms', singular: 'Form', shortname: 'Form' },
    segmentations: { label: 'Segmentations', singular: 'Segmentation', shortname: 'Segmentation' },
    qiusers: { label: 'Users', singular: 'User', shortname: 'User' },
    teams: { label: 'Teams', singular: 'Team', shortname: 'Team' },
    quickMessages: { label: 'Quick Message', singular: 'Quick Messages', shortname: 'Quick Message' },
    broadcasts: { label: 'Boradcasts', singular: 'Broadcast', shortname: 'Broadcast' }
  };
}



/**
 * Get translated module label
 * @param {string} moduleKey - Module key
 * @param {string} property - Property to get (label, singular, shortname)
 * @param {string} defaultValue - Default value if translation not found
 * @returns {string} Translated label
 */
const getModuleTranslation = (moduleKey, property = 'label', defaultValue = '') => {
  if (langModules && langModules[moduleKey] && langModules[moduleKey][property]) {
    return langModules[moduleKey][property];
  }
  return defaultValue;
}

const AppModules = {
  [COLLECTIONS.CALENDAR_COLLECTION_NAME]: {
    gender: 'm',
    module: COLLECTIONS.CALENDAR_COLLECTION_NAME,
    min_role: OPERATOR_ROLE,
    level: OPERATOR_LEVEL,
    label: langModules.calendar?.label || 'Calendário',
    singular: langModules.calendar?.singular || 'Evento',
    shortname: langModules.calendar?.shortname || 'Evento',
    icon: 'fa fa-calendar',
    collection: COLLECTIONS.CALENDAR_COLLECTION_NAME,
    route: `${COLLECTIONS.CALENDAR_COLLECTION_NAME}`,
    routes: ['list'],
    model: calendarModel,
    color: ThemeColors.calendar,
    post_type: 'agenda',
    menu_group: '',
    menu_list: false,
    taxonomies: [],
    public: false,
    toggable: false,
    limited: false,
    included: 0,
  },
  [COLLECTIONS.TASKLISTS_COLLECTION_NAME]: {
    gender: 'f',
    module: COLLECTIONS.TASKLISTS_COLLECTION_NAME,
    min_role: OPERATOR_ROLE,
    level: OPERATOR_LEVEL,
    label: langModules.tasklists?.label || 'Checklists',
    singular: langModules.tasklists?.singular || 'Checklist',
    shortname: langModules.tasklists?.shortname || 'Checklist',
    icon: 'ti-check-box',
    collection: COLLECTIONS.TASKLISTS_COLLECTION_NAME,
    route: `${COLLECTIONS.TASKLISTS_COLLECTION_NAME}`,
    routes: ['list'],
    model: tasklistsModel,
    color: ThemeColors.tasklist,
    post_type: 'tasklist',
    menu_group: '',
    menu_list: false,
    taxonomies: [],
    public: false,
    toggable: false,
    limited: false,
    included: 0,
  },
  [COLLECTIONS.EVENTS_COLLECTION_NAME]: {
    gender: 'm',
    module: COLLECTIONS.EVENTS_COLLECTION_NAME,
    min_role: OWNER_ROLE,
    level: OWNER_LEVEL,
    label: langModules.events?.label || 'Eventos',
    singular: langModules.events?.singular || 'Evento',
    shortname: langModules.events?.shortname || 'Evento',
    icon: 'icon-globe',
    collection: COLLECTIONS.EVENTS_COLLECTION_NAME,
    route: `${COLLECTIONS.EVENTS_COLLECTION_NAME}`,
    routes: ['list', 'detail', 'checkin'],
    model: eventsModel,
    color: ThemeColors.event,
    post_type: 'evento',
    menu_group: 'events',
    taxonomies: [],
    public: false,
    toggable: false,
    limited: true,
    included: 0,
  },
  [COLLECTIONS.CAMPAIGNS_COLLECTION_NAME]: {
    gender: 'f',
    module: COLLECTIONS.CAMPAIGNS_COLLECTION_NAME,
    min_role: OWNER_ROLE,
    level: OWNER_LEVEL,
    label: langModules.campaigns?.label || 'Campanhas',
    singular: langModules.campaigns?.singular || 'Campanha',
    shortname: langModules.campaigns?.shortname || 'Campanha',
    icon: 'ti-announcement',
    collection: COLLECTIONS.CAMPAIGNS_COLLECTION_NAME,
    route: `${COLLECTIONS.CAMPAIGNS_COLLECTION_NAME}`,
    routes: ['list', 'detail', 'models', 'hunters', 'reports'],
    model: campaignsModel,
    color: ThemeColors.campaigns,
    post_type: 'campanha',
    menu_group: 'campaigns',
    sub_menu: {
      models: {
        label: 'menu.models',
        icon: 'ti-envelope',
        path: `/${COLLECTIONS.CAMPAIGNS_COLLECTION_NAME}/models/`,
      },
      hunters: {
        label: 'menu.hunters',
        icon: 'icon-target',
        path: `/${COLLECTIONS.CAMPAIGNS_COLLECTION_NAME}/${COLLECTIONS.HUNTERS_COLLECTION_NAME}/`,
      },
    },
    taxonomies: [],
    public: false,
    toggable: true,
    limited: true,
    included: 0,
  },
  [COLLECTIONS.STORES_COLLECTION_NAME]: {
    gender: 'f',
    module: COLLECTIONS.PRODUCTS_COLLECTION_NAME,
    min_role: OWNER_ROLE,
    level: OWNER_LEVEL,
    label: langModules.stores?.label || 'Lojas',
    singular: langModules.stores?.singular || 'Loja',
    shortname: langModules.stores?.shortname || 'Loja',
    icon: 'icon-home',
    collection: COLLECTIONS.STORES_COLLECTION_NAME,
    route: `${COLLECTIONS.STORES_COLLECTION_NAME}`,
    routes: ['list', 'detail'],
    model: storesModel,
    color: ThemeColors.qiplus,
    post_type: 'loja',
    menu_group: 'stores',
    taxonomies: [],
    public: true,
    toggable: false,
    limited: false,
    included: 0,
  },
  [COLLECTIONS.PRODUCTS_COLLECTION_NAME]: {
    gender: 'm',
    module: COLLECTIONS.PRODUCTS_COLLECTION_NAME,
    min_role: MANAGER_ROLE,
    level: MANAGER_LEVEL,
    label: langModules.products?.label || 'Produtos',
    singular: langModules.products?.singular || 'Produto',
    shortname: langModules.products?.shortname || 'Produto',
    icon: 'icon-social-dropbox',
    collection: COLLECTIONS.PRODUCTS_COLLECTION_NAME,
    route: `${COLLECTIONS.PRODUCTS_COLLECTION_NAME}`,
    routes: ['list', 'detail'],
    model: productsModel,
    color: ThemeColors.primary,
    post_type: 'produto',
    menu_group: 'stores',
    taxonomies: [],
    public: true,
    toggable: false,
    limited: false,
    included: 0,
  },
  /* "barcode_products": {
        gender: "m",
        module: "barcode_products",
        label: "Produtos Catalogados",
        singular: "Produto Catalogado",
        shortname: "Produto Catalogado",
        icon: "fa fa-barcode",
        collection: "barcode_products",
        route: `${"barcode_products"}`,
        routes: ['list','detail'],
        model: barcodeProductsModel,
        color: ThemeColors.primary,
        post_type: "produto-catalogado",
        menu_group: "",
        // menu_group: "stores",
        taxonomies: ["tags"],
        public: true,
        toggable: true,
        limited: true,
        included: 0,
    }, */
  /* "products_lists": {
        gender: "f",
        module: "products_lists",
        min_role: MANAGER_ROLE,
        level: MANAGER_LEVEL,
        label: "Listas de Produtos",
        singular: "Lista de Produtos",
        shortname: "Lista de Produtos",
        shortname: "Lista",
        icon: "ti-list-ol",
        collection: "products_lists",
        route: `${"products_lists"}`,
        routes: ['list','detail'],
        model: productsListsModel,
        color: ThemeColors.primary,
        post_type: "lista-de-produtos",
        menu_group: "",
        // menu_group: "stores",
        taxonomies: ["tags"],
        public: true,
        toggable: true,
        limited: true,
        included: 0,
    }, */
  /* [COLLECTIONS.PACKS_COLLECTION_NAME]: {
        gender: "m",
        module: [COLLECTIONS.PRODUCTS_COLLECTION_NAME],
        min_role: MANAGER_ROLE,
        level: MANAGER_LEVEL,
        label: "Combos",
        singular: "Combo",
        shortname: "Combo",
        icon: "ti-package",
        collection: COLLECTIONS.PACKS_COLLECTION_NAME,
        route: `${COLLECTIONS.PACKS_COLLECTION_NAME}`,
        routes: ['list','detail'],
        model: packsModel,
        color: ThemeColors.primary,
        post_type: "combo",
        menu_group: "",
        // menu_group: "stores",
        taxonomies: ["Categorias, Tags, Perfis, Pontuações"],
        public: true,
        toggable: true,
        limited: true,
        included: 0,
    }, */
  [COLLECTIONS.MAIL_COLLECTION_NAME]: {
    gender: 'm',
    module: COLLECTIONS.MAIL_COLLECTION_NAME,
    label: langModules.mail?.label || 'Emails',
    singular: langModules.mail?.singular || 'Email',
    shortname: langModules.mail?.shortname || 'Email',
    icon: 'icon-envelope',
    collection: COLLECTIONS.MAIL_COLLECTION_NAME,
    route: '',
    routes: [],
    model: mailingModel,
    color: ThemeColors.primary,
    post_type: '',
    menu_group: '',
    taxonomies: [],
    public: false,
    toggable: false,
    limited: false,
    included: 0,
  },
  [COLLECTIONS.CHATS_COLLECTION_NAME]: {
    gender: 'm',
    module: COLLECTIONS.CHATS_COLLECTION_NAME,
    label: langModules.chats?.label || 'Chats',
    singular: langModules.chats?.singular || 'Chat',
    shortname: langModules.chats?.shortname || 'Chat',
    icon: 'icon-bubble',
    collection: COLLECTIONS.CHATS_COLLECTION_NAME,
    route: 'chat',
    routes: [],
    model: {},
    color: ThemeColors.primary,
    post_type: '',
    menu_group: '',
    taxonomies: [],
    public: false,
    toggable: true,
    limited: false,
    included: 0,
  },
  [COLLECTIONS.MAILBOXES_COLLECTION_NAME]: {
    gender: 'f',
    module: COLLECTIONS.MAILBOXES_COLLECTION_NAME,
    label: langModules.mailboxes?.label || 'Caixas de Entrada',
    singular: langModules.mailboxes?.singular || 'Caixa de Entrada',
    shortname: langModules.mailboxes?.shortname || 'Inbox',
    icon: 'icon-envelope',
    collection: COLLECTIONS.MAILBOXES_COLLECTION_NAME,
    route: 'mail',
    routes: [],
    model: {},
    color: ThemeColors.primary,
    post_type: '',
    menu_group: '',
    taxonomies: [],
    public: false,
    toggable: true,
    limited: true,
    included: 0,
  },
  [COLLECTIONS.MAILING_COLLECTION_NAME]: {
    gender: 'm',
    module: COLLECTIONS.MAILING_COLLECTION_NAME,
    label: langModules.mail?.label || 'Emails',
    singular: langModules.mail?.singular || 'Email',
    shortname: langModules.mail?.shortname || 'Email',
    icon: 'icon-envelope',
    collection: COLLECTIONS.MAILING_COLLECTION_NAME,
    route: `${COLLECTIONS.MAILING_COLLECTION_NAME}`,
    routes: ['list', 'detail', 'models', 'reports'],
    model: mailingModel,
    color: ThemeColors.primary,
    post_type: 'email',
    menu_group: 'mailing',
    sub_menu: {
      models: {
        label: 'menu.models',
        icon: 'ti-envelope',
        path: `/${COLLECTIONS.MAILING_COLLECTION_NAME}/models/`,
      },
    },
    taxonomies: [],
    public: true,
    toggable: true,
    limited: false,
    included: 0,
  },
  [COLLECTIONS.LEADS_COLLECTION_NAME]: {
    gender: 'm',
    module: COLLECTIONS.LEADS_COLLECTION_NAME,
    caps: {
      [SELLER_LEVEL]: ['add', 'edit', 'list'],
    },
    label: langModules.leads?.label || 'Leads',
    singular: langModules.leads?.singular || 'Lead',
    shortname: langModules.leads?.shortname || 'Lead',
    icon: 'icon-people',
    collection: COLLECTIONS.LEADS_COLLECTION_NAME,
    route: `${COLLECTIONS.LEADS_COLLECTION_NAME}`,
    routes: ['list', 'detail', 'import', 'tags'],
    model: leadsModel,
    color: ThemeColors.warning,
    post_type: 'leads',
    menu_group: 'contacts',
    sub_menu: {
      import: {
        label: 'menu.import',
        icon: 'ti-import',
        cap: 'import',
        level: MANAGER_LEVEL,
        path: `/${COLLECTIONS.LEADS_COLLECTION_NAME}/import/`,
      },
      tags: {
        label: 'menu.tags',
        icon: 'ti-tags',
        path: `/${COLLECTIONS.LEADS_COLLECTION_NAME}/tags/`,
      },
    },
    taxonomies: [],
    public: false,
    toggable: false,
    limited: false,
    included: 0,
  },
  [COLLECTIONS.DEALS_COLLECTION_NAME]: {
    gender: 'm',
    module: COLLECTIONS.FUNNELS_COLLECTION_NAME,
    min_role: SELLER_ROLE,
    level: SELLER_LEVEL,
    caps: {
      [SELLER_LEVEL]: ['add', 'edit', 'list'],
    },
    label: langModules.deals?.label || 'Negócios',
    singular: langModules.deals?.singular || 'Negócio',
    shortname: langModules.deals?.shortname || 'Negócio',
    icon: 'fa fa-handshake-o',
    collection: COLLECTIONS.DEALS_COLLECTION_NAME,
    route: `${COLLECTIONS.DEALS_COLLECTION_NAME}`,
    routes: ['list', 'detail', 'tags'],
    model: dealsModel,
    color: ThemeColors.deal,
    post_type: 'deals',
    menu_group: 'deals',
    sub_menu: {
      tags: {
        label: 'menu.tags',
        icon: 'ti-tags',
        path: `/${COLLECTIONS.DEALS_COLLECTION_NAME}/tags/`,
      },
    },
    taxonomies: [],
    public: false,
    toggable: false,
    limited: false,
    included: 0,
  },
  [COLLECTIONS.FUNNELS_COLLECTION_NAME]: {
    gender: 'm',
    module: COLLECTIONS.FUNNELS_COLLECTION_NAME,
    min_role: SELLER_ROLE,
    level: SELLER_LEVEL,
    caps: {
      [SELLER_LEVEL]: ['list', 'pipeline'],
    },
    label: langModules.funnels?.label || 'Funis de Vendas',
    singular: langModules.funnels?.singular || 'Funil de Vendas',
    shortname: langModules.funnels?.shortname || 'Funil',
    icon: 'fa fa-filter',
    collection: COLLECTIONS.FUNNELS_COLLECTION_NAME,
    route: `${COLLECTIONS.FUNNELS_COLLECTION_NAME}`,
    routes: ['list', 'detail', 'pipeline'],
    model: funnelsModel,
    color: ThemeColors.info,
    post_type: 'funil',
    menu_group: 'deals',
    taxonomies: [],
    public: false,
    toggable: true,
    limited: true,
    included: 0,
  },
  [COLLECTIONS.AUTOMATIONS_COLLECTION_NAME]: {
    gender: 'f',
    module: COLLECTIONS.AUTOMATIONS_COLLECTION_NAME,
    min_role: MANAGER_ROLE,
    level: MANAGER_LEVEL,
    label: langModules.automations?.label || 'Automações',
    singular: langModules.automations?.singular || 'Automação',
    shortname: langModules.automations?.shortname || 'Automação',
    icon: 'icon-shuffle',
    collection: COLLECTIONS.AUTOMATIONS_COLLECTION_NAME,
    route: `${COLLECTIONS.AUTOMATIONS_COLLECTION_NAME}`,
    routes: ['list', 'detail'],
    model: automationsModel,
    color: ThemeColors.primary,
    post_type: 'automacao',
    menu_group: 'tools',
    taxonomies: [],
    public: false,
    toggable: true,
    limited: true,
    included: 0,
  },
  [COLLECTIONS.FORMS_COLLECTION_NAME]: {
    gender: 'm',
    module: COLLECTIONS.FORMS_COLLECTION_NAME,
    min_role: MANAGER_ROLE,
    level: MANAGER_LEVEL,
    label: langModules.forms?.label || 'Formulários',
    singular: langModules.forms?.singular || 'Formulário',
    shortname: langModules.forms?.shortname || 'Formulário',
    icon: 'ti-layout-accordion-merged',
    collection: COLLECTIONS.FORMS_COLLECTION_NAME,
    route: `${COLLECTIONS.FORMS_COLLECTION_NAME}`,
    routes: ['list', 'detail'],
    model: formsModel,
    color: ThemeColors.yellow,
    post_type: 'formulario',
    menu_group: 'tools',
    taxonomies: [],
    public: true,
    toggable: true,
    limited: true,
    included: 0,
  },
  [COLLECTIONS.FIELDS_COLLECTION_NAME]: {
    gender: 'm',
    module: COLLECTIONS.FIELDS_COLLECTION_NAME,
    label: langModules.customFields?.label || 'Campos Personalizados',
    singular: langModules.customFields?.singular || 'Campo Personalizado',
    shortname: langModules.customFields?.shortname || 'Campo',
    icon: 'ti-layout-accordion-merged',
    collection: COLLECTIONS.FIELDS_COLLECTION_NAME,
    model: fieldsModel,
    color: ThemeColors.yellow,
    post_type: '',
    menu_group: '',
    taxonomies: [],
    public: false,
    toggable: false,
    limited: false,
    included: 0,
  },
  [COLLECTIONS.QUESTIONNAIRES_COLLECTION_NAME]: {
    gender: 'm',
    module: COLLECTIONS.QUESTIONNAIRES_COLLECTION_NAME,
    min_role: MANAGER_ROLE,
    level: MANAGER_LEVEL,
    label: langModules.questionnaires?.label || 'Questionários',
    singular: langModules.questionnaires?.singular || 'Questionário',
    shortname: langModules.questionnaires?.shortname || 'Questionário',
    icon: 'ti-view-list-alt',
    collection: COLLECTIONS.QUESTIONNAIRES_COLLECTION_NAME,
    route: `${COLLECTIONS.QUESTIONNAIRES_COLLECTION_NAME}`,
    routes: ['list', 'detail'],
    model: questionnairesModel,
    color: ThemeColors.primary,
    post_type: 'questionario',
    menu_group: 'tools',
    taxonomies: [],
    public: true,
    toggable: true,
    limited: false,
    included: 0,
  },
  [COLLECTIONS.LANDING_PAGES_COLLECTION_NAME]: {
    gender: 'f',
    module: COLLECTIONS.LANDING_PAGES_COLLECTION_NAME,
    min_role: MANAGER_ROLE,
    level: MANAGER_LEVEL,
    label: 'Landing Pages',
    singular: 'Landing Page',
    shortname: 'Landing',
    icon: 'zmdi zmdi-view-web',
    collection: COLLECTIONS.LANDING_PAGES_COLLECTION_NAME,
    route: `${COLLECTIONS.LANDING_PAGES_COLLECTION_NAME}`,
    routes: ['list', 'detail', 'editor', 'reports'],
    model: landingPagesModel,
    color: ThemeColors.primary,
    post_type: 'landing-page',
    menu_group: 'tools',
    taxonomies: [],
    public: true,
    toggable: true,
    limited: true,
    included: 0,
  },
  [COLLECTIONS.CHECKIN_COLLECTION_NAME]: {
    gender: 'm',
    module: COLLECTIONS.EVENTS_COLLECTION_NAME,
    label: 'Check-in',
    singular: 'Check-in',
    shortname: 'Check-in',
    icon: 'ti-check-box',
    collection: COLLECTIONS.CHECKIN_COLLECTION_NAME,
    model: checkinModel,
    color: ThemeColors.primary,
    post_type: 'lista-de-check-in',
    menu_group: '',
    taxonomies: [],
    public: false,
    toggable: false,
    limited: false,
    included: 0,
  },
  [COLLECTIONS.PARTICIPANTS_COLLECTION_NAME]: {
    gender: 'm',
    module: COLLECTIONS.EVENTS_COLLECTION_NAME,
    label: 'Participantes',
    singular: 'Participante',
    shortname: 'Participante',
    icon: 'icon-people',
    collection: COLLECTIONS.PARTICIPANTS_COLLECTION_NAME,
    model: participantsModel,
    color: ThemeColors.primary,
    post_type: '',
    menu_group: '',
    taxonomies: [],
    public: false,
    toggable: false,
    limited: false,
    included: 0,
  },
  [COLLECTIONS.SEGMENTATIONS_COLLECTION_NAME]: {
    gender: 'f',
    module: COLLECTIONS.LEADS_COLLECTION_NAME,
    min_role: MANAGER_ROLE,
    level: MANAGER_LEVEL,
    label: 'Segmentações',
    singular: 'Segmentação',
    shortname: 'Segmentação',
    icon: 'ti-harddrives',
    collection: COLLECTIONS.SEGMENTATIONS_COLLECTION_NAME,
    route: `${COLLECTIONS.SEGMENTATIONS_COLLECTION_NAME}`,
    routes: ['list', 'detail'],
    model: segmentationsModel,
    color: ThemeColors.primary,
    post_type: 'lista-de-emails',
    menu_group: 'contacts',
    taxonomies: [],
    public: false,
    toggable: false,
    limited: false,
    included: 0,
  },
  [COLLECTIONS.QIUSERS_COLLECTION_NAME]: {
    gender: 'm',
    module: COLLECTIONS.QIUSERS_COLLECTION_NAME,
    min_role: OWNER_ROLE,
    level: OWNER_LEVEL,
    label: langModules.users?.label || 'Usuários',
    singular: langModules.users?.singular || 'Usuário',
    shortname: langModules.users?.shortname || 'Usuário',
    icon: 'ti-id-badge',
    collection: COLLECTIONS.QIUSERS_COLLECTION_NAME,
    route: `${COLLECTIONS.QIUSERS_COLLECTION_NAME}`,
    routes: ['list', 'detail'],
    model: qiusersModel,
    color: ThemeColors.primary,
    post_type: 'users',
    menu_group: 'users',
    /*
        sub_menu: {
            sellers: {
                // subheader: "menu.sellers",
                label: "menu.sellers",
                icon: "fa fa-address-card-o",
                path: "/users/sellers/",
            },
            managers: {
                label: "menu.managers",
                icon: "fa fa-id-card-o",
                path: "/users/managers/",
            },
        },
        */
    taxonomies: [],
    public: false,
    toggable: true,
    limited: true,
    included: 1,
  },
  [COLLECTIONS.TEAMS_COLLECTION_NAME]: {
    gender: 'f',
    module: COLLECTIONS.QIUSERS_COLLECTION_NAME,
    min_role: MANAGER_ROLE,
    level: MANAGER_LEVEL,
    label: langModules.teams?.label || 'Equipes',
    singular: langModules.teams?.singular || 'Equipe',
    shortname: langModules.teams?.shortname || 'Equipe',
    icon: 'fa fa-users',
    collection: COLLECTIONS.TEAMS_COLLECTION_NAME,
    route: `${COLLECTIONS.TEAMS_COLLECTION_NAME}`,
    routes: ['list', 'detail'],
    model: teamsModel,
    color: ThemeColors.primary,
    post_type: 'lista-de-vendedores',
    menu_group: 'users',
    taxonomies: [],
    public: false,
    toggable: false,
    limited: false,
    included: 0,
  },
  [COLLECTIONS.NOTIFICATIONS_COLLECTION_NAME]: {
    gender: 'f',
    module: COLLECTIONS.QIUSERS_COLLECTION_NAME,
    label: 'Notificações',
    singular: 'Notificação',
    shortname: 'Notificação',
    icon: 'icon-bell',
    collection: COLLECTIONS.NOTIFICATIONS_COLLECTION_NAME,
    route: `${COLLECTIONS.NOTIFICATIONS_COLLECTION_NAME}`,
    routes: ['list', 'detail', 'models'],
    model: notificationsModel,
    color: ThemeColors.primary,
    post_type: 'notificacao',
    menu_group: 'mailing',
    sub_menu: {
      models: {
        label: 'menu.models',
        icon: 'ti-envelope',
        path: `/${COLLECTIONS.NOTIFICATIONS_COLLECTION_NAME}/models/`,
      },
    },
    taxonomies: [],
    public: false,
    toggable: false,
    limited: false,
    included: 0,
  },
  [COLLECTIONS.OTHERS_COLLECTION_NAME]: {
    gender: 'f',
    label: 'Outros',
    singular: 'Outro',
    shortname: 'Outros',
    icon: 'icon-bell',
    color: ThemeColors.primary,
    post_type: 'others',
    public: false,
    toggable: false,
    limited: false,
    included: 0,
  },
  [COLLECTIONS.SHOTX_QUICK_MESSAGES_COLLECTION_NAME]: {
    gender: 'f',
    label: langModules.quickMessages?.label || 'Mensagens Rápidas',
    icon: 'ti-target',
    singular: langModules.quickMessages?.singular || 'Mensagem Rápida',
    shortname: langModules.quickMessages?.shortname || 'Mensagem Rápida',
    color: ThemeColors.primary,
    post_type: 'quickMessages',
    public: false,
    toggable: false,
    limited: false,
    included: 0,
  },

  [COLLECTIONS.SHOTX_INTERACTIONS_COLLECTION_NAME]: {
    gender: 'f',
    label: 'Interação',
    icon: 'ti-target',
    singular: 'Interação',
    shortname: 'Interação',
    color: ThemeColors.primary,
    post_type: 'interactions',
    public: false,
    toggable: false,
    limited: false,
    included: 0,
  },
  [COLLECTIONS.SHOTX_SNIPERS_COLLECTION_NAME]: {
    gender: 'f',
    label: 'ShotxFlow',
    icon: 'ti-target',
    singular: 'ShotxFlow',
    shortname: 'ShotxFlow',
    color: ThemeColors.primary,
    post_type: 'snipers',
    public: false,
    toggable: false,
    limited: false,
    included: 0,
  },
  [COLLECTIONS.LOCALE_COLLECTION_NAME]: {
    gender: 'f',
    label: 'Geolocalização',
    singular: 'Geolocalização',
    shortname: 'Geolocalização',
    icon: 'icon-bell',
    color: ThemeColors.primary,
    post_type: 'Geolocalização',
    public: false,
    toggable: false,
    limited: false,
    included: 0,
  },
  [COLLECTIONS.BROADCASTS_COLLECTION_NAME]: {
    gender: 'f',
    label: 'Mensagens em Massa',
    singular: 'Mensagem em Massa',
    shortname: 'Mensagem em Massa',
    icon: 'icon-bell',
    color: ThemeColors.primary,
    post_type: 'Broadcasts',
    public: false,
    toggable: false,
    limited: false,
    included: 0,
  },
  /* [COLLECTIONS.LIVE_POSTS_COLLECTION_NAME]: {
        gender: "m",
        module: [COLLECTIONS.LIVE_POSTS_COLLECTION_NAME],
        min_role: WEBMASTER_ROLE,
        label: "Live Posts",
        singular: "Live Post",
        shortname: "Live Post",
        icon: "ti-pencil-alt",
        collection: COLLECTIONS.LIVE_POSTS_COLLECTION_NAME,
        route: `${COLLECTIONS.LIVE_POSTS_COLLECTION_NAME}`,
        routes: ['list','detail'],
        model: livePostsModel,
        color: ThemeColors.primary,
        post_type: "live-post",
        // menu_group: "",
        taxonomies: [],
        public: true,
        toggable: true,
        limited: true,
        included: 0,
    }, */
  /* [COLLECTIONS.HOT_POSTS_COLLECTION_NAME]: {
        gender: "m",
        module: [COLLECTIONS.HOT_POSTS_COLLECTION_NAME],
        min_role: WEBMASTER_ROLE,
        label: "Hot Posts",
        singular: "Hot Post",
        shortname: "Hot Post",
        icon: "ti-pencil-alt",
        collection: COLLECTIONS.HOT_POSTS_COLLECTION_NAME,
        route: `${COLLECTIONS.HOT_POSTS_COLLECTION_NAME}`,
        routes: ['list','detail'],
        model: hotPostsModel,
        color: ThemeColors.primary,
        post_type: "",
        // menu_group: "",
        taxonomies: [],
        public: true,
        toggable: true,
        limited: true,
        included: 0,
    }, */
  [COLLECTIONS.PRIZES_COLLECTION_NAME]: {
    gender: 'm',
    module: COLLECTIONS.PRIZES_COLLECTION_NAME,
    min_role: MANAGER_ROLE,
    level: MANAGER_LEVEL,
    label: 'Prêmios',
    singular: 'Prêmio',
    shortname: 'Prêmio',
    icon: 'fa fa-trophy',
    collection: COLLECTIONS.PRIZES_COLLECTION_NAME,
    route: `${COLLECTIONS.PRIZES_COLLECTION_NAME}`,
    routes: ['list', 'detail'],
    model: prizesModel,
    color: ThemeColors.primary,
    post_type: 'premio',
    // menu_group: "stores",
    menu_group: '',
    taxonomies: ['Perfis, Pontuações'],
    public: false,
    toggable: true,
    limited: false,
    included: 0,
  },
  [COLLECTIONS.TREEBUSINESS_COLLECTION_NAME]: {
    gender: 'm',
    module: COLLECTIONS.TREEBUSINESS_COLLECTION_NAME,
    min_role: MANAGER_ROLE,
    level: MANAGER_LEVEL,
    label: 'Tree Business',
    singular: 'Tree Business',
    shortname: 'Tree Business',
    icon: 'zmdi zmdi-format-list-bulleted',
    collection: COLLECTIONS.TREEBUSINESS_COLLECTION_NAME,
    route: `${COLLECTIONS.TREEBUSINESS_COLLECTION_NAME}`,
    routes: ['list', 'detail'],
    color: ThemeColors.primary,
    menu_group: 'users',
    taxonomies: [],
    public: false,
    toggable: false,
    limited: false,
    menu_list: false,
    included: 0,
    sub_menu: {
      models: {
        label: 'sidebar.listAction',
        icon: 'zmdi zmdi-format-list-bulleted',
        path: `/${COLLECTIONS.TREEBUSINESS_COLLECTION_NAME}/`,
      },
    }
  },
  /* [COLLECTIONS.RAFFLES_COLLECTION_NAME]: {
        gender: "m",
        module: [COLLECTIONS.RAFFLES_COLLECTION_NAME],
        min_role: MANAGER_ROLE,
        level: MANAGER_LEVEL,
        label: "Sorteios",
        singular: "Sorteio",
        shortname: "Sorteio",
        icon: "ti-ticket",
        collection: COLLECTIONS.RAFFLES_COLLECTION_NAME,
        route: `${COLLECTIONS.RAFFLES_COLLECTION_NAME}`,
        routes: ['list','detail'],
        model: rafflesModel,
        color: ThemeColors.primary,
        post_type: "sorteio",
        menu_group: "",
        // menu_group: "stores",
        taxonomies: [],
        public: false,
        toggable: true,
        limited: true,
        included: 0,
        "config": {
            "types": {1: "Roda da Fortuna", 2: "Caça-Níquel", 3: "Bingo"}
        }
    }, */
  /* "sorteio_evento": {
        gender: "m",
        module: "sorteio_evento",
        min_role: MANAGER_ROLE,
        level: MANAGER_LEVEL,
        label: "Sorteios",
        singular: "Sorteio",
        shortname: "Sorteio",
        icon: "ti-ticket",
        collection: "sorteio_evento",
        route: `${COLLECTIONS.sorteio_evento}`,
        routes: ['list','detail'],
        model: sorteioEventoModel,
        color: ThemeColors.primary,
        post_type: "sorteio_evento",
        menu_group: "",
        taxonomies: [],
        show_ui: false,
        public: false,
        toggable: true,
        limited: true,
        included: 0,
    }, */
  [COLLECTIONS.TAGS_TAXONOMY_NAME]: {
    gender: 'f',
    module: COLLECTIONS.TAGS_TAXONOMY_NAME,
    min_role: MANAGER_ROLE,
    level: MANAGER_LEVEL,
    label: 'Tags',
    singular: 'Tag',
    shortname: 'Tag',
    icon: 'fa fa-tags',
    collection: COLLECTIONS.TAGS_TAXONOMY_NAME,
    model: tagsModel,
    color: ThemeColors.primary,
    post_type: 'tag',
    menu_group: '',
    menu_list: false,
    taxonomies: [],
    public: false,
    toggable: false,
    limited: false,
    included: 0,
  },
  /* [COLLECTIONS.TEMPLATES_COLLECTION_NAME]: {
        gender: "m",
        module: [COLLECTIONS.TEMPLATES_COLLECTION_NAME],
        label: "Templates",
        singular: "Template",
        shortname: "Template",
        icon: "fa fa-file-image-o",
        collection: COLLECTIONS.TEMPLATES_COLLECTION_NAME,
        route: `${COLLECTIONS.TEMPLATES_COLLECTION_NAME}`,
        routes: ['list','detail'],
        model: templatesModel,
        color: ThemeColors.primary,
        post_type: "template",
        // menu_group: "tools",
        taxonomies: [],
        public: false,
        toggable: true,
        limited: true,
        included: 0,
    }, */
  [COLLECTIONS.TICKETS_COLLECTION_NAME]: {
    gender: 'm',
    module: COLLECTIONS.TICKETS_COLLECTION_NAME,
    label: langModules.tickets?.label || 'Pedidos',
    singular: langModules.tickets?.label || 'Pedido',
    shortname: langModules.tickets?.label || 'Pedido',
    min_role: SELLER_ROLE,
    level: SELLER_LEVEL,
    icon: 'ti-receipt',
    collection: COLLECTIONS.TICKETS_COLLECTION_NAME,
    route: `${COLLECTIONS.TICKETS_COLLECTION_NAME}`,
    routes: ['list', 'detail', 'models'],
    model: ticketsModel,
    color: ThemeColors.success,
    post_type: 'ticket',
    menu_group: 'stores',
    sub_menu: {
      models: {
        label: 'menu.models',
        icon: 'ti-receipt',
        path: `/${COLLECTIONS.TICKETS_COLLECTION_NAME}/models/`,
      },
    },
    taxonomies: [],
    public: false,
    toggable: true,
    limited: false,
    included: 0,
  },
  [COLLECTIONS.CONTRACTS_COLLECTION_NAME]: {
    gender: 'm',
    module: COLLECTIONS.CONTRACTS_COLLECTION_NAME,
    label: langModules.contracts?.label || 'Contratos',
    singular: langModules.contracts?.label || 'Contrato',
    shortname: langModules.contracts?.label || 'Contrato',
    icon: 'zmdi zmdi-format-list-bulleted',
    collection: COLLECTIONS.CONTRACTS_COLLECTION_NAME,
    route: `${COLLECTIONS.CONTRACTS_COLLECTION_NAME}`,
    routes: ['list', 'detail', 'models'],
    model: contractsModel,
    color: ThemeColors.primary,
    post_type: 'contrato',
    menu_group: 'stores',
    sub_menu: {
      models: {
        label: 'menu.models',
        icon: 'ti-envelope',
        path: `/${COLLECTIONS.CONTRACTS_COLLECTION_NAME}/models/`,
      },
    },
    taxonomies: [],
    public: false,
    toggable: true,
    limited: false,
    included: 0,
  },
  [COLLECTIONS.TRACKINGS_COLLECTION_NAME]: {
    gender: 'm',
    module: COLLECTIONS.TRACKINGS_COLLECTION_NAME,
    min_role: MANAGER_ROLE,
    level: MANAGER_LEVEL,
    label: langModules.trackingsLeads?.label || 'Rastreamento de Leads',
    singular: langModules.trackingsLeads?.label || 'Rastreamento de Leads',
    shortname: langModules.trackingsLeads?.label || 'Rastreamento',
    icon: 'fa fa-bullseye',
    collection: COLLECTIONS.TRACKINGS_COLLECTION_NAME,
    route: `${COLLECTIONS.TRACKINGS_COLLECTION_NAME}`,
    routes: ['list', 'detail'],
    model: trackingsModel,
    color: ThemeColors.primary,
    post_type: 'tracking-link',
    menu_group: '', // "tools",
    taxonomies: [],
    public: true,
    toggable: true,
    limited: false,
    included: 0,
  },
  [COLLECTIONS.INTEGRATIONS_COLLECTION_NAME]: {
    gender: 'f',
    module: COLLECTIONS.INTEGRATIONS_COLLECTION_NAME,
    min_role: MANAGER_ROLE,
    level: MANAGER_LEVEL,
    label: langModules.integrations?.label || 'Integrações',
    singular: langModules.integrations?.singular || 'Integração',
    shortname: langModules.integrations?.shortname || 'Integração',
    icon: 'ti-loop',
    collection: COLLECTIONS.INTEGRATIONS_COLLECTION_NAME,
    route: `${COLLECTIONS.INTEGRATIONS_COLLECTION_NAME}`,
    routes: ['list', 'detail'],
    model: integrationsModel,
    color: ThemeColors.primary,
    post_type: 'integracao',
    menu_group: 'tools',
    taxonomies: [],
    public: false,
    toggable: false,
    limited: false,
    included: 0,
  },
  //TODO SHOTX
  [COLLECTIONS.SHOTX_COLLECTION_NAME]: {
    gender: 'm',
    module: COLLECTIONS.SHOTX_COLLECTION_NAME,
    min_role: MANAGER_ROLE,
    level: MANAGER_LEVEL,
    label: 'ShotX',
    singular: 'ShotX',
    shortname: 'ShotX',
    icon: 'ti-target',
    collection: COLLECTIONS.SHOTX_COLLECTION_NAME,
    route: `${COLLECTIONS.SHOTX_COLLECTION_NAME}`,
    routes: ['list', 'detail'],
    model: integrationsModel,
    color: ThemeColors.primary,
    post_type: 'shotx',
    menu_group: 'shotx',
    taxonomies: [],
    public: false,
    toggable: true,
    limited: false,
    menu_list: false,
    included: 0,
    sub_menu: {
      models: {
        label: 'sidebar.listAction',
        icon: 'ti-target',
        path: `/${COLLECTIONS.SHOTX_COLLECTION_NAME}/`,
      },
      quickMessages: {
        label: 'quickMessages.module.title',
        icon: 'ti-target',
        path: `/${COLLECTIONS.SHOTX_COLLECTION_NAME}/quick_messages/`
      },
      sniper: {
        label: 'sniper.module.title',
        icon: 'ti-target',
        path: `${SNIPER_URL}`,
        redirect: true,
      },
      scheduler: {
        label: 'messagesBroadcast.module.title',
        icon: 'ti-target',
        path: `/${COLLECTIONS.SHOTX_COLLECTION_NAME}/broadcast`
      },
    }
  },

  [COLLECTIONS.QIPLUS_PLANS_COLLECTION_NAME]: {
    gender: 'm',
    module: COLLECTIONS.QIPLUS_PLANS_COLLECTION_NAME,
    min_role: ADMIN_ROLE,
    level: ADMIN_LEVEL,
    label: langModules?.['qiplus-plans']?.label,
    singular: langModules?.['qiplus-plans']?.singular || 'Plano QIPlus',
    shortname: langModules?.['qiplus-plans']?.shortname || 'Plano',
    icon: 'icon-rocket',
    collection: COLLECTIONS.QIPLUS_PLANS_COLLECTION_NAME,
    route: `${COLLECTIONS.QIPLUS_PLANS_COLLECTION_NAME}`,
    routes: ['list', 'detail'],
    model: qiplusPlansModel,
    color: ThemeColors.primary,
    post_type: 'planos-qiplus',
    menu_group: 'plans',
    taxonomies: [],
    public: true,
    toggable: false,
    limited: false,
    included: 0,
  },
  [COLLECTIONS.FEATURES_PLANS_COLLECTION_NAME]: {
    gender: 'm',
    module: COLLECTIONS.FEATURES_PLANS_COLLECTION_NAME,
    min_role: ADMIN_ROLE,
    level: ADMIN_LEVEL,
    label: langModules.individualPlans?.label || 'Planos Avulsos',
    singular: langModules.individualPlans?.label || 'Plano Avulso',
    shortname: langModules.individualPlans?.label || 'Plano Avulso',
    icon: 'icon-equalizer',
    collection: COLLECTIONS.FEATURES_PLANS_COLLECTION_NAME,
    route: `${COLLECTIONS.FEATURES_PLANS_COLLECTION_NAME}`,
    routes: ['list', 'detail'],
    model: qiplusPlansModel,
    color: ThemeColors.primary,
    post_type: COLLECTIONS.FEATURES_PLANS_COLLECTION_NAME,
    menu_group: '',
    taxonomies: [],
    public: true,
    toggable: false,
    limited: false,
    included: 0,
  },
  [COLLECTIONS.ACCOUNTS_COLLECTION_NAME]: {
    gender: 'f',
    module: COLLECTIONS.ACCOUNTS_COLLECTION_NAME,
    min_role: ADMIN_ROLE,
    level: ADMIN_LEVEL,
    label: langModules.accounts?.label || 'Contas',
    singular: langModules.accounts?.singular || 'Conta',
    shortname: langModules.accounts?.shortname || 'Conta',
    icon: 'icon-user',
    collection: COLLECTIONS.ACCOUNTS_COLLECTION_NAME,
    route: `${COLLECTIONS.ACCOUNTS_COLLECTION_NAME}`,
    routes: ['list', 'detail'],
    model: accountModel,
    color: ThemeColors.primary,
    post_type: 'account',
    menu_group: 'plans',
    taxonomies: [],
    public: false,
    toggable: false,
    limited: false,
    included: 0,
  },
  [COLLECTIONS.AFFILIATES_COLLECTION_NAME]: {
    gender: 'm',
    module: COLLECTIONS.AFFILIATES_COLLECTION_NAME,
    min_role: ADMIN_ROLE,
    level: ADMIN_LEVEL,
    label: langModules.affiliates?.label || 'Afiliados',
    singular: langModules.affiliates?.singular || 'Afiliado',
    shortname: langModules.affiliates?.shortname || 'Afiliado',
    icon: 'fa fa-id-badge',
    collection: COLLECTIONS.AFFILIATES_COLLECTION_NAME,
    route: `${COLLECTIONS.AFFILIATES_COLLECTION_NAME}`,
    routes: ['list', 'detail'],
    model: affiliatesModel,
    color: ThemeColors.primary,
    post_type: '',
    menu_group: 'plans',
    taxonomies: [],
    public: false,
    toggable: false,
    limited: false,
    included: 0,
  },
}

module.exports = AppModules
