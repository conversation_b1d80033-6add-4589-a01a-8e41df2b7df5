import { langMessages } from "Lang/index"

export const QuestionFieldModel = {
  label: 'Nova Pergunta',
  name: 'question_field',
  type: 'repeater',
  instructions: '',
  required: 0,
  collapsed: '',
  min: 0,
  max: 0,
  layout: 'block',
  button_label: langMessages['actions.addField'],
  sub_fields: [
    {
      label: 'Tip<PERSON> de Campo',
      name: 'type',
      type: 'select',
      instructions: '',
      required: 0,
      choices: {
        select: 'Se<PERSON><PERSON> (resposta única)',
        radio: 'Botões de radio (resposta única)',
        checkbox: 'Checkbox (múltipla escolha)',
      },
      default_value: [],
      allow_null: 0,
      multiple: 0,
      ui: 0,
      return_format: 'value',
      ajax: 0,
      placeholder: '',
    },
    {
      label: '<PERSON>gun<PERSON>',
      name: 'question',
      type: 'text',
      instructions: '',
      required: 0,
      default_value: '',
      placeholder: '',
      prepend: '',
      append: '',
      maxlength: '',
    },
    {
      label: 'Obrigat<PERSON><PERSON>',
      name: 'required',
      type: 'boolean',
      instructions: '',
      required: 0,
      message: '',
      default_value: 0,
      ui: 1,
      ui_on_text: '',
      ui_off_text: '',
    },
    {
      label: 'Esco<PERSON><PERSON>',
      name: 'choices',
      type: 'repeater',
      instructions: '',
      required: 0,
      collapsed: '',
      min: 0,
      max: 0,
      layout: 'table',
      button_label: 'Adicionar Escolha',
      sub_fields: [
        {
          label: 'Pontuação',
          name: 'score',
          type: 'number',
          instructions: '',
          required: 0,
          default_value: '',
          placeholder: '',
          prepend: '',
          append: '',
          maxlength: '',
        },
        {
          label: 'Resposta',
          name: 'value',
          type: 'text',
          instructions: 'O rótulo visível do campo',
          required: 0,
          default_value: '',
          placeholder: '',
          prepend: '',
          append: '',
          maxlength: '',
        },
      ],
    },
  ],
}

export default QuestionFieldModel
