import {
    Badge,
    <PERSON>,
    Button,
    Checkbox,
    Chip,
    Container,
    Dialog,
    DialogActions,
    DialogContent,
    DialogContentText,
    DialogTitle,
    Divider,
    FormControlLabel,
    Grid,
    MenuItem,
    Paper,
    Radio,
    RadioGroup,
    Snackbar,
    Switch,
    TextField,
    Tooltip,
    Typography
} from '@material-ui/core';
import { makeStyles, withStyles } from '@material-ui/core/styles';
import {
    AccessTime as AccessTimeIcon,
    Category as CategoryIcon,
    Devices as DevicesIcon,
    FilterList as FilterListIcon,
    Message as MessageIcon,
    People as PeopleIcon,
    Schedule as ScheduleIcon,
    Send as SendIcon,
    Settings as SettingsIcon
} from '@material-ui/icons';
import { Alert } from '@material-ui/lab';
import { getLeadsByShotxPlatform } from 'Actions/LeadsActions';
import { PageTitleBar } from 'Components/index';
import { Loading } from 'Components/Widgets/components/Loading';
import DTPicker from 'Components/Widgets/DateTimePicker';
import { LEADS_COLLECTION_NAME, SEGMENTATIONS_COLLECTION_NAME, SHOTX_COLLECTION_NAME, SHOTX_CRON_COLLECTION_NAME, SHOTX_INSTANCES_COLLECTION_NAME, SHOTX_QUICK_MESSAGES_COLLECTION_NAME, SHOTX_SNIPERS_COLLECTION_NAME, TAGS_TAXONOMY_NAME, TAXONOMIES_COLLECTION_NAME } from 'Constants/AppCollections';
import { FirebaseRepository } from 'FirebaseRef/repository';
import { nowToISO } from 'Helpers';
import { langMessages } from 'Lang/index';
import React, { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet';
import { useSelector } from 'react-redux';
import { useHistory, useLocation, withRouter } from 'react-router-dom';
import IntlMessages from 'Util/IntlMessages';
import { FirestoreRef } from '../../../firebase/index';
import { TimezoneUtil } from '../../../util/country/timezone';
import { deleteBroadcast } from './components/actions';
import InstanceAutocomplete from './components/InstanceAutocomplete';
import LeadList from './components/LeadList';
import MessageAutocomplete from './components/MessageAutocomplete';
import MessagePreview from './components/MessagePreview';
import SearchableList from './components/SearchableList';
import SegmentationsList from './components/SegmentationsList';
import SniperAutocomplete from './components/SniperAutocomplete';
import TagFilter from './components/TagFilter';

const useStyles = makeStyles((theme) => ({
    root: {
        padding: theme.spacing(4),
    },
    paper: {
        padding: theme.spacing(3),
        marginBottom: theme.spacing(3),
        borderRadius: 12,
        boxShadow: '0 3px 10px rgba(0, 0, 0, 0.08)',
        transition: 'all 0.3s ease-in-out',
        '&:hover': {
            boxShadow: '0 5px 15px rgba(0, 0, 0, 0.1)',
            transform: 'translateY(-2px)',
        },
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '4px',
            background: 'linear-gradient(90deg, #e9177c, #ff3b47)',
        },
    },
    formControl: {
        width: '100%',
        marginBottom: theme.spacing(2),
    },
    statsContainer: {
        display: 'flex',
        justifyContent: 'space-around',
        marginTop: theme.spacing(2),
    },
    sectionTitle: {
        display: 'flex',
        alignItems: 'center',
        marginBottom: theme.spacing(2),
        '& svg': {
            marginRight: theme.spacing(1),
            color: '#e9177c',
        },
    },
    inputWithIcon: {
        position: 'relative',
        '& svg': {
            position: 'absolute',
            right: theme.spacing(1),
            top: '50%',
            transform: 'translateY(-50%)',
            color: theme.palette.text.secondary,
        },
    },
    actionButton: {
        borderRadius: 8,
        padding: '10px 24px',
        textTransform: 'none',
        fontWeight: 600,
        boxShadow: '0 4px 10px rgba(233, 23, 124, 0.2)',
        transition: 'all 0.3s ease',
        '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: '0 6px 15px rgba(233, 23, 124, 0.3)',
        },
    },
    intervalSection: {
        background: 'rgba(233, 23, 124, 0.03)',
        padding: theme.spacing(2),
        borderRadius: 8,
        marginTop: theme.spacing(2),
    },
}));

const MessageBroadcastPage = ({ match }) => {

    const history = useHistory();
    const location = useLocation();
    const { message, type } = location.state || {};
    const collections = {
        shotx: SHOTX_COLLECTION_NAME,
        instances: SHOTX_INSTANCES_COLLECTION_NAME,
        leads: LEADS_COLLECTION_NAME,
        taxonomies: TAXONOMIES_COLLECTION_NAME,
        tags: TAGS_TAXONOMY_NAME,
        quickMessages: SHOTX_QUICK_MESSAGES_COLLECTION_NAME,
        snipers: SHOTX_SNIPERS_COLLECTION_NAME,
        segmentations: SEGMENTATIONS_COLLECTION_NAME,
    }

    const repository = new FirebaseRepository();

    const { account, user } = useSelector(state => state.authReducer);
    const shotxAccountId = `${collections.shotx}/${account.id}`
    const whereAccountId = [['accountId', '==', account.id]];
    const whereLead = [['accountId', '==', account.id], ['status', '!=', 'trash']];

    const classes = useStyles();
    const disabledInputs = type?.view || false;
    const [instances, setInstances] = useState([]);
    const [selectedInstance, setSelectedInstance] = useState(message?.instance?.ID || '');
    const [selectedInstances, setSelectedInstances] = useState(message?.instances ? message.instances.map(inst => inst.ID) : []);
    const [multipleInstanceMode, setMultipleInstanceMode] = useState(false);
    const [instanceRandom, setInstanceRandom] = useState(message?.instanceRandom || false);
    const [loadingInstances, setLoadingInstances] = useState(true);

    const [leads, setLeads] = useState([]);
    const [selectedLeads, setSelectedLeads] = useState(message?.contacts || []);
    const [loadingLeads, setLoadingLeads] = useState(false);

    const [tags, setTags] = useState([]);
    const [selectedTags, setSelectedTags] = useState([]);
    const [loadingTags, setLoadingTags] = useState(false);

    const [quickMessages, setQuickMessages] = useState([]);
    const [selectedMessage, setSelectedMessage] = useState(message?.messageId || '');
    const [selectedMessages, setSelectedMessages] = useState(message?.messages ? message.messages.map(msg => msg.id || msg.ID) : []);
    const [multipleMessageMode, setMultipleMessageMode] = useState(false);
    const [messageRandom, setMessageRandom] = useState(message?.messageRandom || false);
    const [messagePreview, setMessagePreview] = React.useState('');
    const [loadingMessages, setLoadingMessages] = useState(true);

    const [snipers, setSnipers] = useState([]);
    const [selectedSniper, setSelectedSniper] = useState(message?.sniperId || '');
    const [loadingSnipers, setLoadingSnipers] = useState(true);

    const [segmentations, setSegmentations] = useState([]);
    const [selectedSegmentations, setSelectedSegmentations] = useState(message?.segmentations || []);
    const [loadingSegmentations, setLoadingSegmentations] = useState(true);

    const [minDelay, setMinDelay] = useState(1);
    const [maxDelay, setMaxDelay] = useState(5);
    const [isScheduled, setIsScheduled] = useState(Boolean(message?.scheduled_date) || false);
    const [scheduledDate, setScheduledDate] = useState(message?.scheduled_date || '');
    const [intervalQty, setIntervalQty] = useState(message?.intervalQty || 1);
    const [intervalUnit, setIntervalUnit] = useState(message?.intervalUnit || 'mm');
    const [sendingStatus, setSendingStatus] = useState({ total: 0, sent: 0, failed: 0 });
    const [sendType, setSendType] = useState(message?.type || 'text');
    const [openCancelBrodcastDialog, setOpenCancelBrodcastDialog] = useState(false);
    const [snackbar, setSnackbar] = useState({
        message: '',
        severity: 'success',
    });

    const SendFormRadio = withStyles({
        root: {
            color: "#e9177c",
            '&$checked': {
                color: "#e9177c",
            },
        },
        checked: {},
    })(Radio);

    const handleSelectLead = (id) => {
        setSelectedLeads((prev) =>
            prev.includes(id) ? prev.filter((leadId) => leadId !== id) : [...prev, id]
        );
    };

    const handleCancelBroadcast = () => {
        deleteBroadcast(message.id);
        handleCloseDialog();
        history.goBack();
    }

    const handleCloseDialog = () => {
        setOpenCancelBrodcastDialog(false);
    }

    const handleOpenDialog = () => {
        setOpenCancelBrodcastDialog(true);
    }
    const handleSelectAll = () => {
        const filteredLeads = leads.filter(
            lead => selectedTags.length === 0 || lead.tags.some(tag => selectedTags.includes(tag))
        );
        setSelectedLeads(filteredLeads.map(lead => lead.id));
    };

    const handleClearSelection = () => {
        setSelectedLeads([]);
    };

    const handleTagSelect = (tag) => {
        setSelectedTags(prev =>
            prev.includes(tag) ? prev.filter(t => t !== tag) : [...prev, tag]
        );
    };

    const handleSelectSegmentation = (id) => {
        setSelectedSegmentations((prev) =>
            prev.includes(id) ? prev.filter((segmentationId) => segmentationId !== id) : [...prev, id]
        );
    }

    const handleSelectAllSegmentations = () => {
        setSelectedSegmentations(segmentations.map(segmentation => segmentation.id));
    }

    const handleClearSelectionSegmentations = () => {
        setSelectedSegmentations([]);
    }

    // Funções para gerenciamento de instâncias múltiplas
    const handleInstancesChange = (instanceIds) => {
        setSelectedInstances(instanceIds);
    };

    const handleSelectAllInstances = (filteredInstanceIds = null) => {
        if (filteredInstanceIds) {
            // Usar IDs filtrados passados pelo componente (excluindo Instagram)
            setSelectedInstances(filteredInstanceIds);
        } else {
            // Fallback: filtrar Instagram manualmente
            const nonInstagramInstances = instances.filter(instance =>
                instance.platform.toLowerCase() !== 'instagram'
            );
            setSelectedInstances(nonInstagramInstances.map(instance => instance.id.toString()));
        }
    };

    const handleClearInstanceSelection = () => {
        setSelectedInstances([]);
        setInstanceRandom(false); // Reset para sequencial quando limpar seleção
    };

    const toggleMultipleInstanceMode = () => {
        setMultipleInstanceMode(!multipleInstanceMode);
        // Limpar seleções ao trocar de modo
        if (!multipleInstanceMode) {
            setSelectedInstance('');
            // Ao ativar modo múltiplo, remover instâncias Instagram das seleções existentes
            const filteredSelectedInstances = selectedInstances.filter(instanceId => {
                const instance = instances.find(inst => inst.id.toString() === instanceId);
                return instance && instance.platform.toLowerCase() !== 'instagram';
            });
            setSelectedInstances(filteredSelectedInstances);
        } else {
            setSelectedInstances([]);
        }
        setInstanceRandom(false); // Reset para sequencial quando trocar de modo
    };

    // Funções para gerenciamento de mensagens múltiplas
    const handleMessagesChange = (messageIds) => {
        setSelectedMessages(messageIds);
    };

    const handleSelectAllMessages = () => {
        setSelectedMessages(quickMessages.map(message => (message.id || message.ID).toString()));
    };

    const handleClearMessageSelection = () => {
        setSelectedMessages([]);
        setMessageRandom(false); // Reset para sequencial quando limpar seleção
    };

    const toggleMultipleMessageMode = () => {
        setMultipleMessageMode(!multipleMessageMode);
        // Limpar seleções ao trocar de modo
        if (!multipleMessageMode) {
            setSelectedMessage('');
        } else {
            setSelectedMessages([]);
        }
        setMessageRandom(false); // Reset para sequencial quando trocar de modo
    };

    const selectedMessageContent = multipleMessageMode
        ? selectedMessages.map(msgId => {
            const msg = quickMessages.find((m) => (m?.id === msgId || m?.ID === msgId));
            return msg?.message || '';
        }).join(' | ')
        : quickMessages.find((m) => (m?.id === selectedMessage || m?.ID === selectedMessage))?.message.toString() || '';
    const previewLead = leads.find(lead => selectedLeads[0] === lead.id);

    useEffect(() => {
        if (!selectedMessageContent || !previewLead) {
            setMessagePreview('');
            return;
        }
        setMessagePreview(selectedMessageContent);
    }, [selectedMessage, selectedLeads]);

    const handleDateChange = (date) => {
        setScheduledDate(date);
    }

    const stopCard = (card) => {
        switch (card) {
            case collections.instances:
                setLoadingInstances(false);
                break;
            case collections.leads:
                setLoadingLeads(false);
                break;
            case collections.tags:
                setLoadingTags(false);
                break;
            case collections.quickMessages:
                setLoadingMessages(false);
                break;
            case collections.snipers:
                setLoadingSnipers(false);
                break;
            case collections.segmentations:
                setLoadingSegmentations(false);
                break;
            default:
                break;
        }
    }

    const resetBroadcast = () => {
        setIsScheduled(false);
        setScheduledDate('');
        setSelectedInstance('');
        setSelectedInstances([]);
        setInstanceRandom(false);
        setSelectedTags([]);
        setSelectedLeads([]);
        setSendType('text');
        setSelectedMessage('');
        setSelectedMessages([]);
        setMessageRandom(false);
        setMessagePreview('');
        setSelectedSniper('');
        setSelectedSegmentations([]);
        setIntervalQty(1);
        setIntervalUnit('mm');
    }

    // Ao iniciar a tela obtem os dados
    useEffect(() => {
        const listeners = {}

        listeners[collections.instances] = repository.listenCollection(`${shotxAccountId}/${collections.instances}`, (instances) => { setInstances(instances); stopCard(collections.instances) });
        listeners[collections.quickMessages] = repository.listenCollection(`${shotxAccountId}/${collections.quickMessages}`, (quickMessages) => { setQuickMessages(quickMessages); stopCard(collections.quickMessages) });
        listeners[collections.segmentations] = repository.listenCollection(collections.segmentations, (segmentations) => { setSegmentations(segmentations); stopCard(collections.segmentations) }, console.log, whereAccountId);
        return () => {
            for (const [key, listener] of Object.entries(listeners)) {
                listener();
            }
        }
    }, []);

    useEffect(() => {

        if (multipleInstanceMode) {
            if (selectedInstances.length > 0) {
                console.log('✅ Modo múltiplo: carregando leads para', selectedInstances.length, 'instâncias');
                setLoadingLeads(true);
                setLoadingTags(true);
                setFilteredLeadsMultiple();
            } else {
                console.log('⚠️ Modo múltiplo: nenhuma instância selecionada, limpando leads');
                setLeads([]);
                stopCard(collections.leads);
            }
        } else {
            if (selectedInstance) {
                console.log('✅ Modo único: carregando leads para instância', selectedInstance);
                setLoadingLeads(true);
                setLoadingTags(true);
                setFilteredLeads();
            } else {
                console.log('⚠️ Modo único: nenhuma instância selecionada, limpando leads');
                setLeads([]);
                stopCard(collections.leads);
            }
        }
    }, [selectedInstance, selectedInstances, multipleInstanceMode]);

    useEffect(() => {
        let subscriptions = {};

        if (sendType === 'sniper' && !subscriptions[collections.snipers]) {

            subscriptions[collections.snipers] = repository.listenCollection(`${shotxAccountId}/${collections.snipers}`, (snipers) => { setSnipers(snipers); stopCard(collections.snipers) });
        }

        return () => {
            for (const [key, listener] of Object.entries(subscriptions)) {
                listener();
            }
        }
    }, [sendType]);

    useEffect(() => {
        if (!leads.length) {
            return
        }
        let subscription = null;

        const tagsInLeads = Array.from(new Set(leads.flatMap(lead => lead.tags)));

        if (tagsInLeads.length > 0) {

            if (subscription) {
                subscription();
            }

            const collection = `${collections.taxonomies}/${collections.leads}/${collections.tags}`
            subscription = repository.listenCollection(collection, (tags) => { setTags(tags); stopCard(collections.tags) }, console.log, [['ID', 'in', tagsInLeads]]);
        } else {
            stopCard(collections.tags);
        }

        return subscription || (() => { })();
    }, [leads]);


    const setFilteredLeads = () => {
        let localInstance = '';
        if (message && message?.instance) {
            localInstance = message?.instance
        } else {
            localInstance = instances.find(instance => instance.id === selectedInstance);
        }

        switch (localInstance?.platform.toLowerCase()) {
            case 'whatsapp':
                getLeadsByShotxPlatform(localInstance, account.id, whereLead).then(leads => {
                    setLeads(leads || []);
                    stopCard(collections.leads)
                })
                break;
            case 'instagram':
                getLeadsByShotxPlatform(localInstance, account.id, whereLead).then(leads => {
                    setLeads(leads || []);
                    stopCard(collections.leads)
                })
                break;
            default:
                break;
        }
    }

    const setFilteredLeadsMultiple = async () => {
        console.log('🔍 setFilteredLeadsMultiple iniciado');
        console.log('📋 selectedInstances:', selectedInstances);
        console.log('🏢 instances disponíveis:', instances.map(i => ({ id: i.id, title: i.title, platform: i.platform })));

        const selectedInstancesData = instances.filter(instance =>
            selectedInstances.includes(instance.id.toString())
        );

        console.log('✅ selectedInstancesData filtradas:', selectedInstancesData.map(i => ({ id: i.id, title: i.title, platform: i.platform })));

        if (selectedInstancesData.length === 0) {
            console.log('❌ Nenhuma instância selecionada válida encontrada');
            setLeads([]);
            stopCard(collections.leads);
            return;
        }

        // Verificar se há instâncias inativas
        const inactiveInstances = selectedInstancesData.filter(instance =>
            instance.status?.state !== 'open'
        );

        if (inactiveInstances.length > 0) {
            const inactiveNames = inactiveInstances.map(inst => inst.title).join(', ');
            console.log('⚠️ Instâncias inativas detectadas:', inactiveNames);
            setSnackbar({
                message: `${langMessages['shotx.broadcast.warning.inactiveInstances']}: ${inactiveNames}`,
                severity: 'warning',
                open: true
            });
        }

        try {
            console.log('🚀 Iniciando busca de leads para', selectedInstancesData.length, 'instâncias');

            const allLeadsPromises = selectedInstancesData.map(async (instance, index) => {
                try {
                    console.log(`📞 Buscando leads para instância ${index + 1}/${selectedInstancesData.length}:`, instance.title, `(${instance.platform})`);
                    const leads = await getLeadsByShotxPlatform(instance, account.id, whereLead);
                    console.log(`✅ Leads encontrados para ${instance.title}:`, leads ? leads.length : 0);
                    return leads || []; // Garantir que sempre retorna um array
                } catch (error) {
                    console.error(`❌ Erro ao buscar leads da instância ${instance.title}:`, error);
                    return [];
                }
            });

            const allLeadsResults = await Promise.all(allLeadsPromises);
            console.log('📊 Resultados de todas as instâncias:', allLeadsResults.map((leads, i) => ({
                instance: selectedInstancesData[i].title,
                count: leads ? leads.length : 0
            })));

            // Combinar todos os leads e remover duplicatas baseado no ID
            const combinedLeads = [];
            const seenIds = new Set();
            let totalProcessed = 0;
            let validLeads = 0;
            let duplicates = 0;
            let invalidPhone = 0;

            allLeadsResults.forEach((leads, index) => {
                if (leads && Array.isArray(leads)) {
                    leads.forEach(lead => {
                        totalProcessed++;
                        if (lead && lead.ID) {
                            if (seenIds.has(lead.ID)) {
                                duplicates++;
                                return;
                            }

                            // Validar telefone antes de adicionar (versão mais flexível)
                            if (isValidPhoneNumberFlexible(lead)) {
                                seenIds.add(lead.ID);
                                combinedLeads.push({
                                    ...lead,
                                    sourceInstance: selectedInstancesData[index].title // Adicionar referência da instância
                                });
                                validLeads++;
                            } else {
                                invalidPhone++;
                                console.log('📞 Lead sem telefone válido:', {
                                    ID: lead.ID,
                                    displayName: lead.displayName,
                                    mobile: lead.mobile,
                                    mobileCC: lead.mobileCC,
                                    phone: lead.phone,
                                    phoneCC: lead.phoneCC
                                });
                            }
                        }
                    });
                }
            });

            console.log('📈 Estatísticas de processamento:', {
                totalProcessed,
                validLeads,
                duplicates,
                invalidPhone,
                finalCount: combinedLeads.length
            });

            setLeads(combinedLeads);
            stopCard(collections.leads);

            // Feedback sobre o resultado
            if (combinedLeads.length === 0 && selectedInstancesData.length > 0) {
                console.log('⚠️ Nenhum lead válido encontrado');
                setSnackbar({
                    message: langMessages['shotx.broadcast.warning.noValidLeads'],
                    severity: 'info',
                    open: true
                });
            } else {
                console.log('✅ Leads carregados com sucesso:', combinedLeads.length);
            }
        } catch (error) {
            console.error('❌ Erro geral ao buscar leads de múltiplas instâncias:', error);
            setLeads([]);
            stopCard(collections.leads);
            setSnackbar({
                message: langMessages['shotx.broadcast.error.loadingLeads'],
                severity: 'error',
                open: true
            });
        }
    }

    // Função auxiliar para validar número de telefone (versão flexível)
    const isValidPhoneNumberFlexible = (lead) => {
        if (!lead) return false;

        // Para WhatsApp, verificar se tem mobile (com ou sem código do país)
        if (lead.mobile && lead.mobile.toString().trim() !== '') {
            const mobile = lead.mobile.toString().replace(/\D/g, '');
            // Aceitar números com pelo menos 8 dígitos (mais flexível)
            if (mobile.length >= 8) {
                return true;
            }
        }

        // Para outros casos, verificar phone (com ou sem código do país)
        if (lead.phone && lead.phone.toString().trim() !== '') {
            const phone = lead.phone.toString().replace(/\D/g, '');
            // Aceitar números com pelo menos 8 dígitos (mais flexível)
            if (phone.length >= 8) {
                return true;
            }
        }

        // Para Instagram, aceitar leads mesmo sem telefone (podem ter outros contatos)
        // Verificar se tem pelo menos um identificador de contato
        if (lead.email && lead.email.toString().trim() !== '') {
            return true;
        }

        return false;
    }

    // Função auxiliar para validar número de telefone (versão original mais restritiva)
    const isValidPhoneNumber = (lead) => {
        if (!lead) return false;

        // Para WhatsApp, verificar se tem mobile e mobileCC
        if (lead.mobileCC && lead.mobile) {
            const mobile = lead.mobile.toString().replace(/\D/g, '');
            return mobile.length >= 10;
        }

        // Para outros casos, verificar phone
        if (lead.phoneCC && lead.phone) {
            const phone = lead.phone.toString().replace(/\D/g, '');
            return phone.length >= 10;
        }

        return false;
    }

    // Função de validação para dados do broadcast
    const validateBroadcastData = () => {
        const errors = [];

        // 1. Validar seleção de instâncias
        if (multipleInstanceMode) {
            if (selectedInstances.length === 0) {
                errors.push(langMessages['shotx.broadcast.validation.noInstancesSelected']);
            } else {
                // Verificar se todas as instâncias selecionadas existem e estão ativas
                const invalidInstances = selectedInstances.filter(instanceId => {
                    const instance = instances.find(inst => inst.id.toString() === instanceId);
                    return !instance || instance.status?.state !== 'open';
                });

                if (invalidInstances.length > 0) {
                    errors.push(langMessages['shotx.broadcast.validation.invalidInstances']);
                }
            }
        } else {
            if (!selectedInstance) {
                errors.push(langMessages['shotx.broadcast.validation.noInstanceSelected']);
            } else {
                const instance = instances.find(inst => inst.id === selectedInstance);
                if (!instance || instance.status?.state !== 'open') {
                    errors.push(langMessages['shotx.broadcast.validation.invalidInstance']);
                }
            }
        }

        // 2. Validar seleção de destinatários (leads OU segmentações)
        if (selectedLeads.length === 0 && selectedSegmentations.length === 0) {
            errors.push(langMessages['shotx.broadcast.validation.noRecipientsSelected']);
        }

        // 3. Validar conteúdo baseado no tipo de envio
        if (sendType === 'text') {
            // Validar mensagens
            if (multipleMessageMode) {
                if (selectedMessages.length === 0) {
                    errors.push(langMessages['shotx.broadcast.validation.noMessagesSelected']);
                } else {
                    // Verificar se todas as mensagens selecionadas existem
                    const invalidMessages = selectedMessages.filter(messageId => {
                        const message = quickMessages.find(msg => (msg.id || msg.ID).toString() === messageId);
                        return !message || !message.message;
                    });

                    if (invalidMessages.length > 0) {
                        errors.push(langMessages['shotx.broadcast.validation.invalidMessages']);
                    }
                }
            } else {
                if (!selectedMessage) {
                    errors.push(langMessages['shotx.broadcast.validation.noMessageSelected']);
                } else {
                    const message = quickMessages.find(msg => (msg.id || msg.ID) === selectedMessage);
                    if (!message || !message.message) {
                        errors.push(langMessages['shotx.broadcast.validation.invalidMessage']);
                    }
                }
            }
        } else if (sendType === 'sniper') {
            // Validar shotflow/sniper
            if (!selectedSniper) {
                errors.push(langMessages['shotx.broadcast.validation.noSniperSelected']);
            } else {
                const sniper = snipers.find(s => s.id === selectedSniper);
                if (!sniper) {
                    errors.push(langMessages['shotx.broadcast.validation.invalidSniper']);
                }
            }
        }

        // 4. Validar agendamento (se aplicável)
        if (isScheduled && !scheduledDate) {
            errors.push(langMessages['shotx.broadcast.validation.noScheduleDateSelected']);
        }

        return errors;
    };

    const saveBroadcast = () => {
        // Validações
        const validationErrors = validateBroadcastData();
        if (validationErrors.length > 0) {
            setSnackbar({
                message: validationErrors.join(', '),
                severity: 'error',
                open: true
            });
            return;
        }

        const scheduled_date = TimezoneUtil.convertToServerTimezone(scheduledDate || nowToISO(), true);

        const schedule = {
            contacts: selectedLeads,
            executed: false,
            executing: false,
            instance: multipleInstanceMode
                ? instances.filter(instance => selectedInstances.includes(instance.id.toString()))
                : instances.find(instance => instance.id === selectedInstance),
            // instances: multipleInstanceMode
            //     ? instances.filter(instance => selectedInstances.includes(instance.id.toString()))
            //     : undefined,
            scheduled_date,
            type: sendType,
            status: 'published',
            intervalQty,
            intervalUnit,
            multipleInstanceMode,
            instanceRandom: multipleInstanceMode && selectedInstances.length > 1 ? instanceRandom : false,
            multipleMessageMode,
            messageRandom: multipleMessageMode && selectedMessages.length > 1 ? messageRandom : false,
        }

        switch (sendType) {
            case 'text':
                schedule.message = selectedMessageContent;
                if (multipleMessageMode) {
                    schedule.messages = quickMessages.filter(msg =>
                        selectedMessages.includes((msg.id || msg.ID).toString())
                    );
                    schedule.messageIds = selectedMessages;
                } else {
                    schedule.messageId = selectedMessage;
                }
                break;
            case 'sniper':
                schedule.sniper = snipers.find(sniper => sniper.id === selectedSniper);
                schedule.sniperId = selectedSniper;
                break;
        }

        if (selectedSegmentations.length > 0) {
            schedule.segmentations = selectedSegmentations
        }
        console.log("SCHEDULE", schedule)
        if (type?.edit) {
            FirestoreRef.collection(SHOTX_CRON_COLLECTION_NAME)
                .doc(message.id)
                .update(schedule)
                .then(() => {
                    setSnackbar({
                        message: langMessages['messagesBroadcast.changed'],
                        severity: 'success',
                    });
                    setSendingStatus({
                        ...sendingStatus,
                        total: sendingStatus.total + selectedLeads.length,
                    });
                })
                .catch(function (error) {
                    setSnackbar({
                        message: langMessages['messagesBroadcast.error'],
                        severity: 'success',
                    });
                    console.log('error', error);
                })
        } else {

            repository.addDoc(SHOTX_CRON_COLLECTION_NAME, schedule).then((res) => {
                setSnackbar({
                    message: isScheduled ? langMessages['messagesBroadcast.schedule.success'] : langMessages['messagesBroadcast.save.success'],
                    severity: 'success',
                });
                setSendingStatus({
                    ...sendingStatus,
                    total: sendingStatus.total + selectedLeads.length,
                });
            })
                .catch((error) => {
                    console.log('error', error);
                    setSnackbar({
                        message: error.message || isScheduled ? langMessages['messagesBroadcast.schedule.error'] : langMessages['messagesBroadcast.schedule.success'],
                        severity: 'error',
                    });
                })
        }

    }

    const handleClose = (event, reason) => {
        setSnackbar({ ...snackbar, message: '' });
    };

    const canNotSendOrSchedule = (
        // Validar instâncias
        (multipleInstanceMode && selectedInstances.length === 0) ||
        (!multipleInstanceMode && !selectedInstance) ||
        // Validar destinatários (leads OU segmentações)
        (selectedLeads.length === 0 && selectedSegmentations.length === 0) ||
        // Validar agendamento
        (!scheduledDate && isScheduled) ||
        // Validar conteúdo baseado no tipo
        (sendType === 'text' && (
            (multipleMessageMode && selectedMessages.length === 0) ||
            (!multipleMessageMode && !selectedMessage)
        )) ||
        (sendType === 'sniper' && !selectedSniper)
    );
    // const canNotSendOrSchedule = ((!selectedInstance || (multipleInstanceMode && selectedInstances.length === 0)) || (selectedLeads.length === 0 && selectedSegmentations.length === 0) || (!scheduledDate && isScheduled) || (!selectedMessage && sendType === 'text') || (!selectedSniper && sendType === 'sniper'));

    return (
        <>
            <Helmet>
                <title>{langMessages['app.appName'] + ' | ' + langMessages['shotx.broadcast.title']}</title>
                <meta name="description" content="QIPlus" />
            </Helmet>
            <PageTitleBar
                title={
                    <div>
                        <Badge badgeContent={0} color="error">
                            {message && type?.edit ?
                                langMessages['shotx.broadcast.title.editing'].replace('[%type]', message.type.toUpperCase()).replace('[%date_schedule]', TimezoneUtil.formatDate(TimezoneUtil.convertToUserTimezone(message.scheduled_date), langMessages['format.date.full.short.year'])) :
                                // <IntlMessages id={'Editando ' + message.type.toUpperCase() + ' ' + TimezoneUtil.convertToUserTimezone(message.scheduled_date)} /> :
                                <IntlMessages id={"shotx.broadcast.title"} />
                            }
                        </Badge>
                    </div>
                }
                match={match}
                history={history}
            />
            <Container className={classes.root}>
                <Grid container spacing={4}>
                    <Grid item xs={12} md={6} lg={6}>
                        {/* Define as regras de envio */}
                        <Paper className={classes.paper}>
                            <div>
                                <div className={classes.sectionTitle}>
                                    <SettingsIcon />
                                    <Typography variant="h6" gutterBottom>
                                        {langMessages['shotx.broadcast.rules']}
                                    </Typography>
                                </div>
                            </div>

                            <Divider style={{ margin: '0 0 16px 0' }} />

                            {/* Define se é agendamento ou envio imediato */}
                            <Box display="flex" alignItems="center" mb={2}>
                                <ScheduleIcon style={{ marginRight: 8, color: '#e9177c' }} />
                                <Typography variant="subtitle1" style={{ fontWeight: 500 }}>
                                    {langMessages['shotx.broadcast.schedule']}
                                </Typography>
                            </Box>

                            <Grid container direction="row" spacing={2}>
                                <Grid item xs={12} md={4} sm={6}>
                                    <FormControlLabel
                                        disabled={disabledInputs}
                                        control={
                                            <Checkbox
                                                checked={isScheduled}
                                                onChange={(e) => setIsScheduled(e.target.checked)}
                                                name="isScheduled"
                                                color="primary"
                                                style={{ color: '#e9177c' }}
                                            />
                                        }
                                        label={
                                            <Typography variant="body2">
                                                {langMessages['actions.scheduled']}
                                            </Typography>
                                        }
                                    />
                                </Grid>
                                {
                                    isScheduled &&
                                    <>
                                        <Grid item xs={12} md={8} sm={6}>
                                            <DTPicker
                                                onChange={handleDateChange}
                                                value={scheduledDate || null}
                                                defaultValue={scheduledDate || null}
                                                clearable={true}
                                                style={{ flex: 1 }}
                                                inputVariant={"outlined"}
                                                disabled={disabledInputs}
                                            />
                                        </Grid>
                                        <Grid item xs={12}>
                                            <Typography variant="body2" color="textSecondary">
                                                {langMessages['shotx.broadcast.schedulAlert']}
                                            </Typography>
                                        </Grid>
                                    </>

                                }
                            </Grid>

                            <div className={classes.intervalSection}>
                                <Box display="flex" alignItems="center" mb={2}>
                                    <AccessTimeIcon style={{ marginRight: 8, color: '#e9177c' }} />
                                    <Typography variant="subtitle1" style={{ fontWeight: 500 }}>
                                        {langMessages['shotx.broadcast.interval'] || 'Intervalo entre mensagens'}
                                    </Typography>
                                    <Tooltip title="Define o tempo de espera entre o envio de cada mensagem" placement="top">
                                        <SettingsIcon style={{ marginLeft: 8, fontSize: 18, color: '#999' }} />
                                    </Tooltip>
                                </Box>

                                <Grid container direction="row" spacing={2}>
                                    <Grid item xs={6} md={6} sm={6}>
                                        <TextField
                                            fullWidth
                                            type="number"
                                            label={langMessages['shotx.broadcast.intervalQty'] || 'Quantidade'}
                                            variant="outlined"
                                            value={intervalQty}
                                            onChange={(e) => setIntervalQty(parseInt(e.target.value) || 1)}
                                            inputProps={{
                                                min: 1,
                                                max: intervalUnit === 'mm' ? 60 : intervalUnit === 'hh' ? 24 : 365,
                                            }}
                                            disabled={disabledInputs}
                                            InputProps={{
                                                startAdornment: <span style={{ marginRight: 8, color: '#999' }}>#</span>,
                                            }}
                                        />
                                    </Grid>
                                    <Grid item xs={6} md={6} sm={6}>
                                        <TextField
                                            fullWidth
                                            select
                                            label={langMessages['shotx.broadcast.intervalUnit'] || 'Unidade'}
                                            variant="outlined"
                                            value={intervalUnit}
                                            onChange={(e) => setIntervalUnit(e.target.value)}
                                            disabled={disabledInputs}
                                        >
                                            <MenuItem value="mm">{langMessages['time.minutes'] || 'Minutos'}</MenuItem>
                                            <MenuItem value="hh">{langMessages['time.hours'] || 'Horas'}</MenuItem>
                                            <MenuItem value="dd">{langMessages['time.days'] || 'Dias'}</MenuItem>
                                        </TextField>
                                    </Grid>
                                </Grid>

                                <Box mt={2} display="flex" justifyContent="flex-end">
                                    <Chip
                                        label={`${intervalQty} ${intervalUnit === 'mm' ? 'minuto(s)' : intervalUnit === 'hh' ? 'hora(s)' : 'dia(s)'} entre mensagens`}
                                        color="primary"
                                        size="small"
                                        variant="outlined"
                                    />
                                </Box>
                            </div>
                        </Paper>

                        {/* Selecione a instância */}
                        <Paper className={classes.paper}>
                            <div className={classes.sectionTitle}>
                                <DevicesIcon />
                                <Typography variant="h6" gutterBottom>
                                    {langMessages['shotx.broadcast.instanceSelect']}
                                </Typography>
                            </div>

                            <Divider style={{ margin: '0 0 16px 0' }} />

                            <Loading loading={loadingInstances} label={langMessages['texts.loading']}>
                                <Box mb={1} display="flex" justifyContent="space-between" alignItems="center">
                                    <Typography variant="body2" color="textSecondary">
                                        {langMessages['shotx.broadcast.selectPlataform']}
                                    </Typography>
                                    <FormControlLabel
                                        control={
                                            <Switch
                                                checked={multipleInstanceMode}
                                                onChange={toggleMultipleInstanceMode}
                                                disabled={disabledInputs}
                                                color="primary"
                                                size="small"
                                            />
                                        }
                                        label={
                                            <Typography variant="caption">
                                                {langMessages['shotx.broadcast.multipleSelection']}
                                            </Typography>
                                        }
                                        labelPlacement="start"
                                    />
                                </Box>
                                <InstanceAutocomplete
                                    disabled={disabledInputs}
                                    instances={instances}
                                    multiple={multipleInstanceMode}
                                    selectedInstanceId={selectedInstance}
                                    selectedInstanceIds={selectedInstances}
                                    onInstanceChange={setSelectedInstance}
                                    onInstancesChange={handleInstancesChange}
                                    onSelectAll={handleSelectAllInstances}
                                    onClearSelection={handleClearInstanceSelection}
                                />

                                {((!multipleInstanceMode && selectedInstance) || (multipleInstanceMode && selectedInstances.length > 0)) && (
                                    <Box mt={2} display="flex" justifyContent="flex-end" flexWrap="wrap" gap={1}>
                                        {multipleInstanceMode ? (
                                            selectedInstances.map(instanceId => {
                                                const instance = instances.find(inst => inst.id.toString() === instanceId);
                                                return instance ? (
                                                    <Chip
                                                        key={instanceId}
                                                        label={instance.platform || 'Plataforma'}
                                                        color="primary"
                                                        size="small"
                                                        variant="outlined"
                                                    />
                                                ) : null;
                                            })
                                        ) : (
                                            <Chip
                                                label={instances.find(instance => instance.id === selectedInstance)?.platform || 'Plataforma'}
                                                color="primary"
                                                size="small"
                                                variant="outlined"
                                            />
                                        )}
                                    </Box>
                                )}

                                {/* Controle de Ordem das Instâncias - aparece apenas no modo múltiplo com 2+ instâncias */}
                                {multipleInstanceMode && selectedInstances.length > 1 && (
                                    <Box mt={2} p={2} bgcolor="rgba(0, 0, 0, 0.02)" borderRadius={8}>
                                        <Typography variant="subtitle2" gutterBottom>
                                            {langMessages['shotx.broadcast.instanceOrder']}
                                        </Typography>
                                        <RadioGroup
                                            row
                                            value={instanceRandom ? 'random' : 'sequential'}
                                            onChange={(event) => setInstanceRandom(event.target.value === 'random')}
                                        >
                                            <FormControlLabel
                                                value="sequential"
                                                control={<Radio color="primary" size="small" />}
                                                label={
                                                    <Typography variant="body2">
                                                        {langMessages['shotx.broadcast.instanceSequential']}
                                                    </Typography>
                                                }
                                                disabled={disabledInputs}
                                            />
                                            <FormControlLabel
                                                value="random"
                                                control={<Radio color="primary" size="small" />}
                                                label={
                                                    <Typography variant="body2">
                                                        {langMessages['shotx.broadcast.instanceRandom']}
                                                    </Typography>
                                                }
                                                disabled={disabledInputs}
                                            />
                                        </RadioGroup>
                                    </Box>
                                )}
                            </Loading>
                        </Paper>

                        <Paper className={classes.paper}>
                            <div className={classes.sectionTitle}>
                                <MessageIcon />
                                <Typography variant="h6" gutterBottom>
                                    {langMessages['shotx.broadcast.sendTypeSelect']}
                                </Typography>
                            </div>

                            <Divider style={{ margin: '0 0 16px 0' }} />

                            <Box mb={1}>
                                <Typography variant="body2" color="textSecondary">
                                    {langMessages['shotx.broadcast.selectContent']}
                                </Typography>
                            </Box>

                            {/* Alternar entre enviar mensagen ou atribuir um sniper */}
                            <Box display="flex" justifyContent="center" mt={2} mb={1}>
                                <Box
                                    onClick={() => !disabledInputs && setSendType('text')}
                                    p={2}
                                    border={1}
                                    borderColor={sendType === 'text' ? 'primary.main' : 'grey.300'}
                                    borderRadius={8}
                                    bgcolor={sendType === 'text' ? 'rgba(233, 23, 124, 0.05)' : 'transparent'}
                                    mr={2}
                                    style={{
                                        cursor: disabledInputs ? 'default' : 'pointer',
                                        width: '45%',
                                        textAlign: 'center',
                                        transition: 'all 0.3s ease'
                                    }}
                                >
                                    <MessageIcon style={{ fontSize: 36, color: sendType === 'text' ? '#e9177c' : '#999', marginBottom: 8 }} />
                                    <Typography variant="body1" style={{ fontWeight: sendType === 'text' ? 600 : 400 }}>
                                        {langMessages['shotx.broadcast.sendTypeText']}
                                    </Typography>
                                </Box>

                                <Box
                                    onClick={() => !disabledInputs && setSendType('sniper')}
                                    p={2}
                                    border={1}
                                    borderColor={sendType === 'sniper' ? 'primary.main' : 'grey.300'}
                                    borderRadius={8}
                                    bgcolor={sendType === 'sniper' ? 'rgba(233, 23, 124, 0.05)' : 'transparent'}
                                    style={{
                                        cursor: disabledInputs ? 'default' : 'pointer',
                                        width: '45%',
                                        textAlign: 'center',
                                        transition: 'all 0.3s ease'
                                    }}
                                >
                                    <SendIcon style={{ fontSize: 36, color: sendType === 'sniper' ? '#e9177c' : '#999', marginBottom: 8 }} />
                                    <Typography variant="body1" style={{ fontWeight: sendType === 'sniper' ? 600 : 400 }}>
                                        {langMessages['shotx.broadcast.sendTypeSniper']}
                                    </Typography>
                                </Box>
                            </Box>

                            <Box display="none">
                                <RadioGroup
                                    row
                                    name="sendType"
                                    value={sendType}
                                    onChange={(e) => setSendType(e.target.value)}
                                >
                                    <FormControlLabel
                                        value="text"
                                        control={<SendFormRadio />}
                                        label={langMessages['shotx.broadcast.sendTypeText']}
                                        disabled={disabledInputs}
                                    />
                                    <FormControlLabel
                                        value="sniper"
                                        control={<SendFormRadio />}
                                        label={langMessages['shotx.broadcast.sendTypeSniper']}
                                        disabled={disabledInputs}
                                    />
                                </RadioGroup>
                            </Box>
                        </Paper>

                        {/* Selecione a mensagem */}
                        {sendType === 'text' && <Paper className={classes.paper}>
                            <div className={classes.sectionTitle}>
                                <MessageIcon />
                                <Typography variant="h6" gutterBottom>
                                    {langMessages['shotx.broadcast.messageSelect']}
                                </Typography>
                            </div>

                            <Divider style={{ margin: '0 0 16px 0' }} />

                            <Box mb={1} display="flex" justifyContent="space-between" alignItems="center">
                                <Typography variant="body2" color="textSecondary">
                                    {langMessages['shotx.broadcast.selectQuickMessage']}
                                </Typography>
                                <FormControlLabel
                                    control={
                                        <Switch
                                            checked={multipleMessageMode}
                                            onChange={toggleMultipleMessageMode}
                                            disabled={disabledInputs}
                                            color="primary"
                                            size="small"
                                        />
                                    }
                                    label={
                                        <Typography variant="caption">
                                            {langMessages['shotx.broadcast.messageMultipleSelection']}
                                        </Typography>
                                    }
                                    labelPlacement="start"
                                />
                            </Box>

                            <Loading loading={loadingMessages} label={langMessages['texts.loading']}>
                                <MessageAutocomplete
                                    disabled={disabledInputs}
                                    messages={quickMessages}
                                    multiple={multipleMessageMode}
                                    selectedMessage={selectedMessage}
                                    selectedMessageIds={selectedMessages}
                                    onMessageChange={setSelectedMessage}
                                    onMessagesChange={handleMessagesChange}
                                    onSelectAll={handleSelectAllMessages}
                                    onClearSelection={handleClearMessageSelection}
                                />

                                {((!multipleMessageMode && selectedMessage) || (multipleMessageMode && selectedMessages.length > 0)) && (
                                    <Box mt={2} display="flex" justifyContent="flex-end" flexWrap="wrap" gap={1}>
                                        {multipleMessageMode ? (
                                            selectedMessages.map(messageId => {
                                                const message = quickMessages.find(msg => (msg.id || msg.ID).toString() === messageId);
                                                return message ? (
                                                    <Chip
                                                        key={messageId}
                                                        label={message.title || 'Mensagem'}
                                                        color="primary"
                                                        size="small"
                                                        variant="outlined"
                                                    />
                                                ) : null;
                                            })
                                        ) : (
                                            <Chip
                                                label={quickMessages.find(m => m.id === selectedMessage || m.ID === selectedMessage)?.title || 'Mensagem selecionada'}
                                                color="primary"
                                                size="small"
                                                variant="outlined"
                                            />
                                        )}
                                    </Box>
                                )}

                                {/* Controle de Ordem das Mensagens - aparece apenas no modo múltiplo com 2+ mensagens */}
                                {multipleMessageMode && selectedMessages.length > 1 && (
                                    <Box mt={2} p={2} bgcolor="rgba(0, 0, 0, 0.02)" borderRadius={8}>
                                        <Typography variant="subtitle2" gutterBottom>
                                            {langMessages['shotx.broadcast.messageOrder']}
                                        </Typography>
                                        <RadioGroup
                                            row
                                            value={messageRandom ? 'random' : 'sequential'}
                                            onChange={(event) => setMessageRandom(event.target.value === 'random')}
                                        >
                                            <FormControlLabel
                                                value="sequential"
                                                control={<Radio color="primary" size="small" />}
                                                label={
                                                    <Typography variant="body2">
                                                        {langMessages['shotx.broadcast.messageSequential']}
                                                    </Typography>
                                                }
                                                disabled={disabledInputs}
                                            />
                                            <FormControlLabel
                                                value="random"
                                                control={<Radio color="primary" size="small" />}
                                                label={
                                                    <Typography variant="body2">
                                                        {langMessages['shotx.broadcast.messageRandom']}
                                                    </Typography>
                                                }
                                                disabled={disabledInputs}
                                            />
                                        </RadioGroup>
                                    </Box>
                                )}
                            </Loading>
                        </Paper>}

                        {/* Selecione o sniper */}
                        {sendType === 'sniper' && <Paper className={classes.paper}>
                            <div className={classes.sectionTitle}>
                                <SendIcon />
                                <Typography variant="h6" gutterBottom>
                                    {langMessages['shotx.broadcast.sniperSelect']}
                                </Typography>
                            </div>

                            <Divider style={{ margin: '0 0 16px 0' }} />

                            <Box mb={1}>
                                <Typography variant="body2" color="textSecondary">
                                    {langMessages['shotx.broadcast.selectSniper']}
                                </Typography>
                            </Box>

                            <Loading loading={loadingSnipers} label={langMessages['texts.loading']}>
                                <SniperAutocomplete
                                    snipers={snipers}
                                    disabled={disabledInputs}
                                    selectedSniper={selectedSniper}
                                    onSniperChange={setSelectedSniper}
                                />

                                {selectedSniper && (
                                    <Box mt={2} display="flex" justifyContent="flex-end">
                                        <Chip
                                            label={snipers.find(s => s.id === selectedSniper)?.title || 'Sniper selecionado'}
                                            color="primary"
                                            size="small"
                                            variant="outlined"
                                        />
                                    </Box>
                                )}
                            </Loading>
                        </Paper>}

                        {sendType === 'text' && previewLead && (
                            <Paper className={classes.paper}>
                                <div className={classes.sectionTitle}>
                                    <MessageIcon />
                                    <Typography variant="h6" gutterBottom>
                                        {langMessages['shotx.broadcast.messagePreview']}
                                    </Typography>
                                </div>

                                <Divider style={{ margin: '0 0 16px 0' }} />

                                <Box mb={1}>
                                    <Typography variant="body2" color="textSecondary">
                                        {langMessages['shotx.broadcast.selectQuickMessageView']}
                                    </Typography>
                                </Box>

                                <Box mt={2} p={2} bgcolor="rgba(233, 23, 124, 0.03)" borderRadius={8}>
                                    <MessagePreview
                                        message={messagePreview}
                                        lead={previewLead}
                                    />
                                </Box>
                            </Paper>
                        )}
                    </Grid>
                    <Grid item xs={12} md={6} lg={6}>
                        {/* Selecione os leads */}
                        <Paper className={classes.paper}>
                            <div className={classes.sectionTitle}>
                                <PeopleIcon />
                                <Typography variant="h6" gutterBottom>
                                    {langMessages['shotx.broadcast.leadsSelect']}
                                </Typography>
                            </div>

                            <Divider style={{ margin: '0 0 16px 0' }} />

                            <Box mb={1}>
                                <Typography variant="body2" color="textSecondary">
                                    {langMessages['shotx.broadcast.selectLeads']}
                                </Typography>
                            </Box>

                            {
                                selectedInstance && tags.length > 0 && (
                                    <>
                                        <Box display="flex" alignItems="center" mb={1}>
                                            <FilterListIcon style={{ marginRight: 8, color: '#e9177c', fontSize: 20 }} />
                                            <Typography variant="subtitle2">
                                                Filtrar por tags
                                            </Typography>
                                        </Box>

                                        <Loading loading={loadingTags} label={langMessages['texts.loading']}>
                                            <Box mb={2} p={2} bgcolor="rgba(233, 23, 124, 0.03)" borderRadius={8}>
                                                <TagFilter
                                                    tags={tags}
                                                    selectedTags={selectedTags}
                                                    onTagSelect={handleTagSelect}
                                                />
                                            </Box>
                                        </Loading>
                                    </>
                                )
                            }

                            <Loading loading={loadingLeads} label={langMessages['texts.loading']}>
                                {
                                    leads.length > 0 && (
                                        (!multipleInstanceMode && selectedInstance) ||
                                        (multipleInstanceMode && selectedInstances.length > 0)
                                    ) && (
                                        <>
                                            <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                                                <Typography variant="subtitle2">
                                                    {langMessages['shotx.broadcast.leadsSelectLabel']}
                                                </Typography>

                                                {selectedLeads.length > 0 && (
                                                    <Chip
                                                        label={`${selectedLeads.length} contato(s) selecionado(s)`}
                                                        color="primary"
                                                        size="small"
                                                    />
                                                )}
                                            </Box>

                                            <SearchableList
                                                list={leads}
                                                searchFields={['firstName', 'lastName', 'email', 'phone']}
                                                label={langMessages['shotx.broadcast.leadsSelectLabel']}
                                                disabled={disabledInputs}
                                                renderList={(leads) => (
                                                    <LeadList
                                                        leads={leads}
                                                        disabled={disabledInputs}
                                                        selectedLeads={selectedLeads}
                                                        onSelectLead={handleSelectLead}
                                                        onSelectAll={handleSelectAll}
                                                        onClearSelection={handleClearSelection}
                                                        selectedTags={selectedTags}
                                                        tags={tags}
                                                    />
                                                )}
                                            >
                                            </SearchableList>
                                        </>
                                    )
                                }
                                {
                                    leads.length <= 0 && !loadingLeads &&
                                    ((!multipleInstanceMode && !selectedInstance) || (multipleInstanceMode && selectedInstances.length === 0)) && (
                                        <Box p={3} textAlign="center" bgcolor="rgba(0, 0, 0, 0.03)" borderRadius={8}>
                                            <DevicesIcon style={{ fontSize: 48, color: '#999', marginBottom: 16 }} />
                                            <Typography>
                                                {multipleInstanceMode
                                                    ? langMessages['shotx.broadcast.selectInstances']
                                                    : langMessages['shotx.broadcast.selectInstance']
                                                }
                                            </Typography>
                                        </Box>
                                    )
                                }
                                {
                                    leads.length <= 0 && (
                                        (!multipleInstanceMode && selectedInstance) ||
                                        (multipleInstanceMode && selectedInstances.length > 0)
                                    ) && (
                                        <Box p={3} textAlign="center" bgcolor="rgba(0, 0, 0, 0.03)" borderRadius={8}>
                                            <PeopleIcon style={{ fontSize: 48, color: '#999', marginBottom: 16 }} />
                                            <Typography>{langMessages['shotx.broadcast.noLeads']}</Typography>
                                        </Box>
                                    )
                                }
                            </Loading>
                        </Paper>

                        {/* Selecione as segmentações */}
                        <Paper className={classes.paper}>
                            <div className={classes.sectionTitle}>
                                <CategoryIcon />
                                <Typography variant="h6" gutterBottom>
                                    {langMessages['shotx.broadcast.segmentationsSelect']}
                                </Typography>
                            </div>

                            <Divider style={{ margin: '0 0 16px 0' }} />

                            <Box mb={1}>
                                <Typography variant="body2" color="textSecondary">
                                    {langMessages['shotx.broadcast.selectSegmentations']}
                                </Typography>
                            </Box>

                            <Loading loading={loadingSegmentations} label={langMessages['texts.loading']}>
                                {segmentations.length > 0 ? (
                                    <>
                                        <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                                            <Typography variant="subtitle2">
                                                {langMessages['shotx.broadcast.segmentationsSelectLabel']}
                                            </Typography>

                                            {selectedSegmentations.length > 0 && (
                                                <Chip
                                                    label={`${selectedSegmentations.length} segmentação(ões) selecionada(s)`}
                                                    color="primary"
                                                    size="small"
                                                />
                                            )}
                                        </Box>

                                        <SearchableList
                                            list={segmentations}
                                            searchFields={['title']}
                                            label={langMessages['shotx.broadcast.segmentationsSelectLabel']}
                                            disabled={disabledInputs}
                                            renderList={(segmentations) => (
                                                <SegmentationsList
                                                    segmentations={segmentations}
                                                    disabled={disabledInputs}
                                                    selectedSegmentations={selectedSegmentations}
                                                    onSelectSegmentation={handleSelectSegmentation}
                                                    onSelectAll={handleSelectAllSegmentations}
                                                    onClearSelection={handleClearSelectionSegmentations}
                                                />
                                            )}
                                        >
                                        </SearchableList>
                                    </>
                                ) : (
                                    <Box p={3} textAlign="center" bgcolor="rgba(0, 0, 0, 0.03)" borderRadius={8}>
                                        <CategoryIcon style={{ fontSize: 48, color: '#999', marginBottom: 16 }} />
                                        <Typography>{langMessages['shotx.broadcast.noSegmentations']}</Typography>
                                    </Box>
                                )}
                            </Loading>
                        </Paper>


                        {
                            !disabledInputs &&
                            <Paper className={classes.paper} style={{ marginTop: 24 }}>
                                <div className={classes.sectionTitle}>
                                    <SendIcon />
                                    <Typography variant="h6" gutterBottom>
                                        {langMessages['button.complete']}
                                    </Typography>
                                </div>

                                <Divider style={{ margin: '0 0 16px 0' }} />

                                <Box mb={2}>
                                    <Typography variant="body2" color="textSecondary">
                                        {canNotSendOrSchedule
                                            ? langMessages['shotx.broadcast.completAllCamps']
                                            : langMessages['shotx.broadcast.allCampsOk']}
                                    </Typography>
                                </Box>

                                <Box display="flex" style={{ gap: '16px' }}>
                                    <Button
                                        variant="contained"
                                        color="primary"
                                        onClick={saveBroadcast}
                                        fullWidth
                                        disabled={canNotSendOrSchedule}
                                        className={classes.actionButton}
                                        startIcon={<SendIcon />}
                                    >
                                        {
                                            type?.edit ?
                                                langMessages['button.save'] :
                                                isScheduled ? langMessages['shotx.broadcast.sendLater'] : langMessages['shotx.broadcast.sendNow']
                                        }
                                    </Button>
                                    {
                                        type?.edit &&
                                        <Button
                                            variant="outlined"
                                            className="btn-danger text-white"
                                            onClick={handleOpenDialog}
                                            fullWidth
                                            style={{ borderRadius: 8 }}
                                        >
                                            {langMessages['shotx.broadcast.schedule.cancel']}
                                        </Button>
                                    }
                                </Box>

                                {canNotSendOrSchedule && (
                                    <Box mt={2} p={2} bgcolor="rgba(255, 55, 57, 0.05)" borderRadius={8}>
                                        <Typography variant="body2" color="error">
                                            <strong>{langMessages['alerts.atention']}:</strong> {langMessages['shotx.broadcast.checkInputs']}.
                                            {isScheduled && !scheduledDate && <span> {langMessages['shotx.broadcast.requiredSchedule']}.</span>}
                                        </Typography>
                                    </Box>
                                )}
                            </Paper>
                        }
                    </Grid>
                    <Dialog
                        open={openCancelBrodcastDialog}
                        onClose={handleCloseDialog}
                        aria-labelledby="alert-dialog-title"
                        aria-describedby="alert-dialog-description"
                        PaperProps={{
                            style: {
                                borderRadius: 12,
                                padding: 8
                            }
                        }}
                    >
                        <DialogTitle id="alert-dialog-title">
                            <Box display="flex" alignItems="center">
                                <SendIcon style={{ marginRight: 8, color: '#e9177c' }} />
                                {langMessages['shotx.broadcast.cancel.dialog.title']}
                            </Box>
                        </DialogTitle>
                        <DialogContent>
                            <DialogContentText id="alert-dialog-description">
                                {langMessages['shotx.broadcast.cancel.dialog.description']}
                            </DialogContentText>
                        </DialogContent>
                        <DialogActions>
                            <Button
                                onClick={handleCloseDialog}
                                variant='outlined'
                                className="btn-primary text-white"
                                style={{ borderRadius: 8 }}
                            >
                                {langMessages['quickMessages.no']}
                            </Button>
                            <Button
                                onClick={handleCancelBroadcast}
                                className="btn-danger text-white"
                                variant='outlined'
                                color="danger"
                                style={{ borderRadius: 8 }}
                            >
                                {langMessages['quickMessages.yes']}
                            </Button>
                        </DialogActions>
                    </Dialog>
                </Grid>
            </Container>

            <Snackbar
                anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
                open={Boolean(snackbar.message)}
                onClose={handleClose}
                autoHideDuration={5000}
            >
                <Alert onClose={handleClose} severity={snackbar.severity}>
                    {snackbar.message}
                </Alert>
            </Snackbar>
        </>
    );
};

export const AddShotxBroadcast = withRouter(MessageBroadcastPage)
