const AppModules = require('../../constants/AppModules')
const AppTaxonomies = require('../../constants/AppTaxonomies')

const es_ES_Strings = {
  "accounts.account": "Cuenta",
  "accounts.accountSettings": "Configuraciones de la cuenta",
  "accounts.accountType": "Tipo de cuenta",
  "accounts.brand": "Marca",
  "accounts.corporation": "Entidad legal",
  "accounts.corporation": "Entidad legal",
  "accounts.createAccountOny": "Crear solo la cuenta",
  "accounts.createSubscription": "Crear firma",
  "accounts.createSubscriptionOnSave":
    "¿Quieres crear una suscripción ahora?Si la cuenta tiene un afiliado es informar al afiliado antes de crear la primera firma",
  "accounts.hasPendingUpdate":
    "Su cuenta tiene una actualización pendiente.Pagar para habilitarlo",
  "accounts.inactiveAccount": "Inactivo",
  "accounts.individual": "Individual",
  "accounts.invoice": "Factura",
  "accounts.invoices": "Facturas",
  "accounts.mainUser": "Usuario de la cuenta principal",
  "accounts.noLeadsOptions":
    "El plan no permite seleccionar la cantidad de leads",
  "accounts.parentAccount": "Cuenta matriz",
  "accounts.qtdLeads": "Dirige",
  "accounts.settings": "Ajustes",
  "accounts.slogan": "Eslogan",
  "accounts.updateMyPlan": "Actualizar mi plan",
  "accounts.updatePlan": "Actualizar el plan",
  "accounts.updateSubscription": "Actualizar suscripción",
  "accounts.upgrade": "Plan de actualización",
  "actions.add.brodcast": "Agregar mensaje de masa",
  "actions.addConditions": "Agregar condiciones",
  "actions.addCustomField": "Crear campo personalizado",
  "actions.addDealTags": "Agregar etiquetas al negocio",
  "actions.addField": "Agregar Campo",
  "actions.addLeadTags": "Agregar etiquetas para contactar",
  "actions.addNewDeal": "Crear un negocio",
  "actions.addScore": "Agregar puntuación",
  "actions.addTags": "Agregar etiquetas",
  "actions.addTasklist": "Agregue una lista de tareas",
  "actions.addToCampaign": "Agregar a la campaña",
  "actions.addToEvent": "Agregar al evento",
  "actions.addToFunnel": "Agregar al embudo",
  "actions.addToSegmentation": "Agregar a la segmentación",
  "actions.addToStore": "Agregar a la tienda",
  "actions.assign": "Designado",
  "actions.assignManager": "Gerente de cuentas designado",
  "actions.assignProfile": "Perfil designado",
  "actions.assignSeller": "Vendedor designado",
  "actions.assignTasklistToDeal": "Asignar tareas",
  "actions.assignTasklistToTeam": "Designar tareas a un equipo",
  "actions.assignTasklistToUser": "Designar tareas a un usuario",
  "actions.assignTeam": "Equipo designado",
  "actions.assignUser": "Usuario designado",
  "actions.back": "Volver",
  "actions.changeFunnel": "Cambiar",
  "actions.changeToFunnel": "Cambiar al embudo",
  "actions.conditions": "Condiciones",
  "actions.continue": "Continuar",
  "actions.createEmbed": "Aumentar",
  "actions.createModel": "Crear modelo",
  "actions.createShortlink": "Crea un enlace corto",
  "actions.createTicket": "Generar una solicitud",
  "actions.dealWebhook": "Enviar negocios por webhook",
  "actions.deleteConditions": "Eliminar las condiciones",
  "actions.generateContract": "Generar contrato",
  "actions.gotoAction": "Salta la otra acción",
  "actions.gotoAutomation": "Iniciar otra automatización",
  "actions.gotoReset": "Haga clic para redefinir la acción vinculada",
  "actions.gotoTooltip": "Arrastre el objetivo a la acción deseada",
  "actions.importCustomField": "Importar campo personalizado",
  "actions.instructions.updateDeal":
    "Actualiza un negocio existente en el embudo seleccionado",
  "actions.manageAttachments": "Editar anexos",
  "actions.newImport": "Nueva importación",
  "actions.other": "Otras acciones",
  "actions.preview": "Avance",
  "actions.removeDealTags": "Eliminar las etiquetas de negocios",
  "actions.removeLeadTags": "Eliminar las etiquetas de contacto",
  "actions.removeTags": "Eliminar etiquetas",
  "actions.saveConditions": "Ahorrar",
  "actions.schedule": "Para programar",
  "actions.scheduled": "Programado",
  "actions.selectContacTitle": "Seleccione un contacto",
  "actions.sendBroadcast": "Transmisión",
  "actions.sendContract": "Enviar",
  "actions.sendEmail": "Enviar correo electrónico",
  "actions.sendMessage": "Mensaje",
  "actions.sendNotificationToManager": "Notificar al administrador de cuentas",
  "actions.sendNotificationToSeller": "Notificar al vendedor",
  "actions.sendTestEmail": "Enviar correo electrónico de prueba",
  "actions.sendshotx": "shotx Qiplus",
  "actions.sendshotxToSeller": "shotx Qiplus - Vendedor",
  "actions.shotxSendMessageText": "Enviar mensaje de texto",
  "actions.stageBackInFunnel": "Progreso",
  "actions.stageForwardInFunnel": "Pelusa",
  "actions.testEmail": "Prueba de envío",
  "actions.testImapUser": "Prueba la configuración de IMAP",
  "actions.testSMTPUser": "Prueba la configuración de SMTP",
  "actions.timer": "Minutero",
  "actions.updateDeal": "Actualizar un negocio",
  "actions.webhook": "Enviar contacto por webhook",
  "affiliate.accountType.conta_corrente": "Cuenta de cheques",
  "affiliate.accountType.conta_corrente_conjunta": "Articulación de DC",
  "affiliate.accountType.conta_poupanca": "Cuenta de ahorros",
  "affiliate.accountType.conta_poupanca_conjunta": "Ahorro de la articulación",
  "affiliate.commission": "Comisión",
  "affiliate.commissions": "Comisiones",
  "affiliate.data": "Datos de afiliados",
  "affiliate.data.document_number": "Documento",
  "affiliate.data.email": "Correo electrónico",
  "affiliate.data.name": "Nombre completo",
  "affiliate.data.phone_numbers": "Teléfono móvil",
  "affiliate.data.site_url": "Sitio web",
  "affiliate.data.type": "tipo de cuenta",
  "affiliate.termLink": "Enlace de enlace de afiliación",
  "affiliate.transfer.auto": "Pago automático",
  "affiliate.transfer.daily": "A diario",
  "affiliate.transfer.day": "Día de pago",
  "affiliate.transfer.interval": "Frecuencia de pago",
  "affiliate.transfer.monthly": "Mensual",
  "affiliate.transfer.weekly": "Semanalmente",
  "affiliates.deleteConfirmationBody":
    "El afiliado será eliminado permanentemente",
  "affiliates.deleteConfirmationTitle":
    "¿Estás seguro de que quieres eliminar a este afiliado?",
  "affiliates.duplicatedMsg": "Ya hay un afiliado con estos datos",
  "affiliates.switchToPost": "¿Quieres editar el afiliado?",
  "alerts.accountWillBeSavedBefore":
    "Atención, la cuenta se guardará con la configuración actual antes de realizar esta operación",
  "alerts.actionsWouldBeDeleted":
    "Hay acciones en la automatización que se perderían para eliminar este paso.Elimine las acciones o modifique el paso al que pertenecen antes de eliminar el paso",
  "alerts.atention": "Atención",
  "alerts.cardUpdateNotAllowed":
    "No es posible cambiar la tarjeta mientras la firma está en vigor",
  "alerts.changeModel":
    "Al cambiar el modelo, se perderá el contenido editado.¿Quieres continuar?",
  "alerts.contentWasModifiedByUser":
    "El contenido que se editó fue modificado por otro usuario",
  "alerts.contentWasModifiedInDB":
    "El contenido que se está editando ha sido modificado por otro usuario o automatización en la base de datos",
  "alerts.doingCheckout":
    "Estamos configurando su cuenta.Mantenga la ventana del navegador abierta hasta que se complete el pago.",
  "alerts.dontHaveData": "No hay datos que se muestren",
  "alerts.emailAlreadyExists":
    "Ya hay un usuario con el correo electrónico informado.Use otro correo electrónico o recupere los datos de la cuenta.",
  "alerts.emailChangeNotAllowed":
    "No está permitido el cambio en el correo electrónico de la cuenta",
  "alerts.emailExists":
    "Ya hay un usuario con el correo electrónico informado.",
  "alerts.emailSMTPSuccess":
    "La configuración de SMTP se configuró correctamente",
  "alerts.emailTestFailed":
    "No fue posible enviar el correo electrónico de prueba",
  "alerts.emailTestSuccess": "¡Correo electrónico de prueba exitoso!",
  "alerts.emptyAccountOwner": "Ingrese al usuario principal de la cuenta",
  "alerts.emptyDisplayName": "Ingrese un nombre de exposición",
  "alerts.emptyEmailAddress": "Ingrese un correo electrónico",
  "alerts.emptyFirstName": "Ingrese un nombre",
  "alerts.emptyRecipients": "Seleccionar destinatarios",
  "alerts.emptyRoles": "Designar al menos una función al usuario",
  "alerts.emptyStart": "Ingrese la fecha de inicio",
  "alerts.emptyTitle": "Ingrese un título",
  "alerts.imapUserAuthError": "No fue posible establecer la conexión IMAP",
  "alerts.imapUserAuthSuccess": "Conexión IMAP establecida con éxito",
  "alerts.informCountryCode": "Ingrese el código de país y el DDD",
  "alerts.informFullPhoneNumber":
    "Ingrese el teléfono con código de país y DDD",
  "alerts.invalidPassword": "Contraseña no válida",
  "alerts.itemsLimitReached": "Límite de artículos alcanzados",
  "alerts.mailboxNotSchedulable":
    "El envío a través de cuadros de entrada no permite la programación y/o envío futuros a toda la base de plomo.El tiroteo se realizará de inmediato y no aparecerá en la lista de envíos.",
  "alerts.minLengthPassord6": "La contraseña debe ser al menos 6 dígitos",
  "alerts.missingLoginData": "Usuario o contraseña no válidos",
  "alerts.mobileViewportDetected":
    "Está utilizando una resolución de pantalla inferior a la indicada para operar en la plataforma.La resolución mínima sugerida es 1280x800",
  "alerts.noChangesToSave": "No hay modificaciones para guardar",
  "alerts.notAccountOwner": "El contenido no pertenece a la cuenta corriente",
  "alerts.onChangeBillingData":
    "Al modificar los datos de facturación, al completar el pago se generará una nueva firma con una fecha de inicio en la fecha actual.",
  "alerts.onlyUpgradeAllowed":
    "El valor del nuevo plan debe ser mayor que el plan actual para verificar una firma activa",
  "alerts.operationWillBeAborted":
    "La operación en curso se perderá y no se puede recuperar más tarde",
  "alerts.phoneAlreadyExists":
    "Ya hay un usuario con el teléfono informado.Use otro teléfono o recupere los datos de la cuenta.",
  "alerts.plaeaseRelateFields": "Seleccione los campos para la asociación",
  "alerts.pleaseCompletePersonalInfo": "Complete su registro",
  "alerts.pleaseInformValidCPF": "Ingrese un CPF válido",
  "alerts.pleaseInformYourCPF": "Por favor ingrese su CPF",
  "alerts.pleaseInformYourDisplayName":
    "Por favor ingrese su nombre de usuario",
  "alerts.pleaseInformYourEmail": "Por favor ingrese su correo electrónico",
  "alerts.pleaseInformYourEmailAndPassword":
    "Ingrese el correo electrónico y la contraseña",
  "alerts.pleaseInformYourMobileNumber": "Ingrese el número de móvil",
  "alerts.pleaseInformYourPassword": "Ingrese su contraseña",
  "alerts.resetViewsConfirm":
    "Esto cero las vistas explicadas hasta ahora.¿Estás seguro de que quieres continuar?",
  "alerts.subscriptionWilBeUpdated":
    "Al verificar su cuenta, se actualizará y solo se le cobrará el monto proporcional del nuevo plan para el tiempo de suscripción restante, sin implicar la modificación de la fecha de renovación o los montos cobrados anteriormente.",
  "alerts.tooManyRequests":
    "Has superado el número máximo de intentos de inicio de sesión.Inténtalo de nuevo más tarde",
  "alerts.updateToBoletoNotAllowed":
    "No es posible cambiar la forma de pago del boleto de la tarjeta",
  "alerts.userAlreadyExists":
    "Ya hay un usuario con los datos informados.Cree un nuevo usuario o recupere los datos de la cuenta.",
  "alerts.userIsNotActive":
    "OPS, parece que su usuario no está activo en este momento",
  "alerts.userNotRegistered":
    "Todavía no tiene un usuario activo en Qi Plus.Regístrese ahora o comuníquese con nosotros para obtener más información sobre Qiplus.",
  "alerts.validateYourEmailAddress":
    "Valia su correo electrónico antes de enjuiciar",
  "alerts.verifyFormErrors":
    "Verifique los errores del formulario antes de enjuiciar",
  "alerts.weakPassword": "Elija una contraseña con al menos 6 caracteres",
  "alerts.welcomeToAffiliateProgram":
    "Bienvenido al programa de afiliados de Qiplus",
  "alerts.wrongPassword": "Contraseña no válida",
  "alerts.youMightLooseYourChanges":
    "Alguna información que se edita puede perderse mediante modificaciones externas.",
  "app.appName": "Qiplus",
  "app.slogan": "Convierte a tus clientes en fanáticos",
  "app.sloganAction": "¡Comience ahora convierte a tus clientes en fanáticos!",
  "app.updatedMsg": "Solicitud procesada exitosa!",
  "app.welcome": "¡Bienvenido a Qi Plus!",
  "ask.displayName": "¿Cómo prefieres que te llamen?",
  "attachments.download": "Descargar",
  "attachments.downloadAll": "Descargar todos los archivos adjuntos",
  "automations.fired": "Disparos",
  "automations.lastActionAlert":
    "Esta acción solo se puede agregar al final de una secuencia de acciones.",
  "automations.loopAlert":
    "No es posible agregar el mismo elemento en los desencadenantes y la automatización.",
  "automations.possibleLoopDanger":
    "Esta acción no se puede realizar ya que podría generar un bucle infinito.",
  "button.4Steps": "Revise los 4 pasos para comenzar su operación de ventas",
  "button.accept": "Para aceptar",
  "button.acceptTerms": "Aceptar términos",
  "button.add": "Para agregar",
  "button.addNew": "Nuevo",
  "button.addNewUser": "Agregar usuario",
  "button.addOption": "Añadir opción",
  "button.addOptions": "Agregar opciones",
  "button.addTriggerGroup": "Nuevo conjunto de reglas",
  "button.assignNow": "Asignar ahora",
  "button.associate": "Dirigir",
  "button.back": "Volver",
  "button.blockLevelButton": "Botón de nivel de bloque",
  "button.button": "Botón",
  "button.cancel": "Cancelar",
  "button.click": "Hacer clic",
  "button.clone": "Clon",
  "button.close": "Para cerrar",
  "button.complete": "Finalizar",
  "button.completeStep": "Paso completo",
  "button.confirm": "Confirmar",
  "button.connect": "Conectar",
  "button.convert": "Convertir",
  "button.copy": "Copiar",
  "button.create": "Para crear",
  "button.createNewLead": "Crea un nuevo plomo",
  "button.cropImage": "Imagen",
  "button.danger": "Peligro",
  "button.delete": "Eliminar",
  "button.discard": "Desechar",
  "button.disconnectAccount": "Desconectar de la cuenta",
  "button.downloadPdfReport": "Descargar informe PDF",
  "button.edit": "Editar",
  "button.editOption": "Opción de edición",
  "button.editOptions": "Opciones de edición",
  "button.editTicket": "Modificar solicitud",
  "button.error": "Error",
  "button.exclude": "Borrar",
  "button.exportToExcel": "Exportar a Excel",
  "button.goToCampaign": "Ir a la campaña",
  "button.hide": "Esconder",
  "button.import": "Importar",
  "button.info": "Información",
  "button.largeButton": "Botón grande",
  "button.learnMore": "Ver Más",
  "button.leave": "Salir",
  "button.like": "Como",
  "button.link": "Enlace",
  "button.lost": "Perdido",
  "button.more": "Más",
  "button.newLead": "Nuevo plomo",
  "button.newOption": "Nueva opción",
  "button.newOptions": "Nuevas opciones",
  "button.next": "Próximo",
  "button.nfConfirm": "Emisión de factura",
  "button.nfConfirmNotazz": "Emisión de factura en nota",
  "button.no": "No",
  "button.ok": "DE ACUERDO",
  "button.openMenu": "Menú abierto",
  "button.openPopover": "Abierta Popover",
  "button.openWithFadeTransition": "Abierto con transición de desvanecimiento",
  "button.pen": "Bolígrafo",
  "button.preview": "Avance",
  "button.primary": "Primario",
  "button.primaryButton": "Botón principal",
  "button.reassignUsers": "Cuenta designada",
  "button.reject": "Desechar",
  "button.remove": "Eliminar",
  "button.removeAll": "Eliminar",
  "button.removeTriggerGroup": "Eliminar el conjunto de reglas",
  "button.reply": "Para responder",
  "button.reset": "Para limpiar",
  "button.resetField": "Campo",
  "button.resetFilters": "Paso",
  "button.resetStyles": "Restaurar estilos",
  "button.resetViews": "Cero vistas",
  "button.save": "Ahorrar",
  "button.saveAndContinue": "Guardar y continuar editando",
  "button.saveAsDraft": "Rascar",
  "button.saveChanges": "Ahorrar",
  "button.saveDraft": "Chatarra",
  "button.saveNow": "Ahorrar",
  "button.search": "Buscar",
  "button.secondary": "Goteo",
  "button.seeInsights": "Ver ideas",
  "button.select": "Seleccionar",
  "button.send": "Para enviar",
  "button.sendMessage": "Mensaje",
  "button.sendTicket": "Solicitud de finalización",
  "button.settings": "Configurar",
  "button.show": "Espectáculo",
  "button.signIn": "Acceso",
  "button.signUp": "Crear una cuenta",
  "button.smallButton": "Botón pequeño",
  "button.success": "Sucesión",
  "button.support": "Apoyo",
  "button.tryAgain": "Intentar otra vez",
  "button.understood": "Comprendido",
  "button.undo": "Deshacer",
  "button.undoExternalChanges": "Deshacer modificaciones externas",
  "button.update": "Para actualizar",
  "button.useDefaultImage": "Usar imagen predeterminada",
  "button.view": "Ver",
  "button.viewAll": "Ver todo",
  "button.viewLess": "Ver menos",
  "button.viewMore": "Ver más",
  "button.viewProfile": "Ver perfil",
  "button.warning": "Alerta",
  "button.won": "Ganar",
  "button.writeNewMessage": "Nuevo mensaje",
  "button.yes": "Sí",
  "calendar.agenda": "Orden del día",
  "calendar.agendas": "Agendas",
  "calendar.allDay": "Todo el día",
  "calendar.date": "Fecha",
  "calendar.dates": "Fechas",
  "calendar.day": "Día",
  "calendar.days": "Días",
  "calendar.deleteConfirmationBody": "El evento será eliminado permanentemente",
  "calendar.deleteConfirmationTitle":
    "¿Estás seguro de que quieres eliminar este evento?",
  "calendar.event": "Evento",
  "calendar.hour": "Tiempo",
  "calendar.hours": "Horas",
  "calendar.info": "Datos de eventos",
  "calendar.minute": "Minuto",
  "calendar.minutes": "Minutos",
  "calendar.month": "Mes",
  "calendar.month.abr": "Abril",
  "calendar.month.ago": "Agosto",
  "calendar.month.dez": "Diciembre",
  "calendar.month.fev": "Febrero",
  "calendar.month.jan": "Enero",
  "calendar.month.jul": "Julio",
  "calendar.month.jun": "Junio",
  "calendar.month.mai": "Puede",
  "calendar.month.mar": "Marzo",
  "calendar.month.nov": "Noviembre",
  "calendar.month.out": "Octubre",
  "calendar.month.set": "Septiembre",
  "calendar.months": "Meses",
  "calendar.newEvent": "Nuevo evento",
  "calendar.next": "Siguiente",
  "calendar.previous": "Anterior",
  "calendar.second": "Segundo",
  "calendar.seconds": "Segundo",
  "calendar.time": "Tiempo",
  "calendar.today": "Hoy",
  "calendar.tomorrow": "Mañana",
  "calendar.updateEvent": "Evento de edición",
  "calendar.week": "Semana",
  "calendar.weeks": "Semanas",
  "calendar.work_week": "Días hábiles",
  "calendar.year": "Año",
  "calendar.years": "Años",
  "calendar.yesterday": "Ayer",
  "campaigns.campaignHomeUrl": "URL de campaña principal",
  "campaigns.hunter.actions": "Comportamiento",
  "campaigns.hunter.analytics.clickDetails.byElement": "Clicks de elementos",
  "campaigns.hunter.analytics.clickDetails.byElementDescription":
    "Seleccione un elemento para ver los detalles de clic",
  "campaigns.hunter.analytics.clickDetails.byElementId":
    "Haga clic por ID en: Elemento",
  "campaigns.hunter.analytics.clickDetails.title": "Haga clic en Detalles",
  "campaigns.hunter.analytics.eventsEvolution.title":
    "Evolución de los acontecimientos",
  "campaigns.hunter.analytics.filters": "Filtros",
  "campaigns.hunter.analytics.filters.byQueryParamName":
    "Filtrar por nombre de parámetro",
  "campaigns.hunter.analytics.filters.byQueryParamValue":
    "Filtrar por valor de parámetro",
  "campaigns.hunter.analytics.filters.byRoute": "Girar",
  "campaigns.hunter.analytics.filters.byRouteTooltip":
    "Filtrar para las rutas que tuvieron acceso durante el período seleccionado",
  "campaigns.hunter.analytics.filters.clear": "Para limpiar",
  "campaigns.hunter.analytics.filters.loading": "Cargando...",
  "campaigns.hunter.analytics.filters.noOptions": "No hay filtro disponible",
  "campaigns.hunter.analytics.filters.open": "Abierto",
  "campaigns.hunter.analytics.noClicks": "No hay clic registrado",
  "campaigns.hunter.analytics.noData": "No hay datos para mostrar",
  "campaigns.hunter.analytics.title": "Métricas de QI Hunter",
  "campaigns.hunter.cancel": "Cancelar",
  "campaigns.hunter.clone": "Copiar",
  "campaigns.hunter.close": "Para cerrar",
  "campaigns.hunter.code": "Código de QI Hunter",
  "campaigns.hunter.codeEnd": "Fin de Qi Hunter",
  "campaigns.hunter.codeStart": "Inicio del QI Hunter",
  "campaigns.hunter.copied": "Copiado en el área de transferencia",
  "campaigns.hunter.copy": "Copiar",
  "campaigns.hunter.create": "Crea QI Hunter",
  "campaigns.hunter.created": "QI Hunter creado con éxito",
  "campaigns.hunter.createdAt": "Creado en",
  "campaigns.hunter.createFailed": "Fracaso al crear QI Hunter",
  "campaigns.hunter.creating": "Creando QI Hunter",
  "campaigns.hunter.delete": "Borrar",
  "campaigns.hunter.description":
    "Crea y administra tus píxeles de seguimiento",
  "campaigns.hunter.domain": "Dominio",
  "campaigns.hunter.duplicate": "Duplicado",
  "campaigns.hunter.edit": "Editar  QI Hunter",
  "campaigns.hunter.events.clicked": "Hacer clics",
  "campaigns.hunter.events.empty":
    "No hay evento seleccionado para el seguimiento",
  "campaigns.hunter.events.select": "Eventos para rastrear",
  "campaigns.hunter.events.title": "Eventos",
  "campaigns.hunter.events.viewed": "Vistas",
  "campaigns.hunter.events.warning":
    "Copie y actualice el Código QI Hunter para los cambios que se reflejarán.",
  "campaigns.hunter.gridView": "Visualización de la cuadrícula",
  "campaigns.hunter.listView": "Vista de lista",
  "campaigns.hunter.name": "Nombre de QI Hunter",
  "campaigns.hunter.new": "Nuevo QI Hunter",
  "campaigns.hunter.newDescription": "Descripción del QI Hunter",
  "campaigns.hunter.stats": "Estadística",
  "campaigns.hunter.status": "Estado",
  "campaigns.hunter.statusDisabled": "Desactivado",
  "campaigns.hunter.statusEnabled": "Activado",
  "campaigns.hunter.statusToggle": "Activar/deshabilitar",
  "campaigns.hunter.tags": "Etiquetas",
  "campaigns.hunter.title": "QI Hunter",
  "campaigns.hunter.update": "Actualizar QI Hunter",
  "campaigns.hunter.updated": "QI Hunter actualizado con éxito",
  "campaigns.hunter.updatedAt": "Actualizado en",
  "campaigns.hunter.updateFailed": "No actualizar el QI Hunter",
  "campaigns.performance": "Desempeño de la campaña",
  "cart.deleteWarning": "Las modificaciones en el orden se perderán",
  "chat.contactInfo": "Información de contacto",
  "chat.deleteGroup": "Eliminar el grupo",
  "chat.filterContacts": "Contacto",
  "chat.group": "Grupos",
  "chat.leaveChat": "Eliminar la conversación",
  "chat.leaveGroup": "Grupo",
  "chat.newGroup": "Nuevo grupo",
  "chat.noContactsSelected": "Sin contacto seleccionado",
  "chat.renameGroup": "Cambiar el nombre de grupo",
  "chat.selectContacts": "Seleccionar contactos",
  "chat.sendToMultipleRecipients": "Enviar a varios destinatarios",
  "chat.startChatTip":
    "Elija un contacto o usuario para comenzar una conversación",
  "chat.startChatTip2": "Prueba",
  "chat.typeAContactName": "Ingrese el nombre de contacto",
  "chat.typeAGroupName": "Ingrese el nombre del grupo",
  "chat.typeAMessage": "Escribe tu mensaje",
  "chat.writeAMessage": "Escribe tu mensaje",
  "checklist.add": "Nueva lista de verificación",
  "checklist.clone": "Clonación de la lista de verificación",
  "checklist.create": "Lista de verificación",
  "checklist.edit": "Editar lista de verificación",
  "checklists.clone": "Listas de verificación",
  "checklists.edit": "Editar listas de verificación",
  "commissions.canceled": "Cancelado",
  "commissions.ended": "Finalizado",
  "commissions.paid": "Pagado",
  "commissions.pending_payment": "Pendiente",
  "commissions.trialing": "Ensayo",
  "commissions.unpaid": "No pagado",
  "components.addNewTasks": "Nueva tarea",
  "components.address": "DIRECCIÓN",
  "components.address2Optional": "Dirección 2 (opcional)",
  "components.addToCart": "añadir a la cesta",
  "components.advancedSettings": "Configuración avanzada",
  "components.ageRange": "Edad",
  "components.all": "Todo",
  "components.approve": "Aprobar",
  "components.automation": "Automatización",
  "components.automations": "Automatización",
  "components.basic": "Básico",
  "components.basicChip": "Chips básico",
  "components.basicFeatures": "Características básicas",
  "components.billingAddress": "Dirección de Envio",
  "components.billingData": "Datos de facturación",
  "components.buyNow": "Comprar ahora",
  "components.cancelled": "Cancelado",
  "components.cardNumber": "Número de tarjeta",
  "components.cart": "Coche de compras",
  "components.CartEmptyText": "El coche de compras está vacío",
  "components.changeBillingData": "Modificar datos de facturación",
  "components.checkout": "Verificar",
  "components.choose": "Para elegir",
  "components.city": "Ciudad",
  "components.clickableChip": "Haga clic en Chip",
  "components.companyName": "Nombre de empresa",
  "components.compose": "Componer",
  "components.confirmPasswords": "Confirmar Contraseña",
  "components.country": "País",
  "components.customIcon": "Icono personalizado",
  "components.customStyle": "Estilo personalizado",
  "components.cvv": "CV",
  "components.data": "Datos",
  "components.do": "De",
  "components.done": "Hecho",
  "components.dontHaveAccountSignUp": "No tengo cuenta de registro",
  "components.drafts": "Rascar",
  "components.email": "Correo electrónico",
  "components.emailPrefrences": "Preferencias de correo electrónico",
  "components.enterEmailAddress": "Ingrese la dirección de correo electrónico",
  "components.enterUserName": "Ingrese el nombre de usuario",
  "components.expansionPanel1": "Panel de expansión 1",
  "components.expansionPanel2": "Panel de expansión 2",
  "components.expiryDate": "Madurez",
  "components.facebookAds": "Anuncios de Facebook",
  "components.facebookReports": "Informes de Facebook",
  "components.firstName": "Nombre",
  "components.followers": "Seguidores",
  "components.funnelActions": "Comportamiento",
  "components.generalSetting": "Configuración básica",
  "components.goToHomePage": "Ir a la página de inicio",
  "components.goToShop": "Ir a la tienda",
  "components.integration": "Integración",
  "components.integrations": "Integración",
  "components.item": "Artículo",
  "components.items": "Elementos",
  "components.last1Month": "Mes pasado",
  "components.last6Month": "Últimos 6 meses",
  "components.last7Days": "Últimos 7 días",
  "components.lastName": "Apellido",
  "components.leadEntrances": "Entrada de plomo",
  "components.left": "Izquierda",
  "components.mobileNumber": "Teléfono móvil",
  "components.month": "Mes",
  "components.myProfile": "Mi perfil",
  "components.nameOnCard": "Nombre en la tarjeta",
  "components.NoItemFound": "No se encuentra el artículo",
  "components.occupation": "Profesión",
  "components.openAlert": "Alerta abierta",
  "components.openFullScreenDialog": "Abrir diálogo de pantalla completa",
  "components.pageNotfound": "Página no encontrada",
  "components.paid": "Pagado",
  "components.passwordPrompt": "Información de contraseña",
  "components.passwords": "Contraseñas",
  "components.payment": "Pago",
  "components.payNow": "Pagar ahora",
  "components.pending": "Pendiente",
  "components.persistentDrawer": "Cajón persistente",
  "components.phoneNo": "Teléfono",
  "components.pipeline": "Tubería",
  "components.pipelines": "Tuberías",
  "components.placeOrder": "Orden",
  "components.popularity": "Popularidad",
  "components.print": "Imprimir",
  "components.product": "Producto",
  "components.projectName": "Nombre del proyecto",
  "components.prompt": "Inmediato",
  "components.quantity": "Cantidad",
  "components.refunded": "Reembolsado",
  "components.removeProduct": "Eliminar el producto",
  "components.right": "Bien",
  "components.saveContinue": "Guardar y continuar",
  "components.sendMessage": "Enviar un mensaje",
  "components.sent": "Adjunto",
  "components.settings": "Ajustes",
  "components.ShippingAddressText":
    "La dirección de envío es la misma que la dirección de facturación.",
  "components.signIn": "Acceso",
  "components.slideInAlertDialog": "Deslice en el diálogo de alerta",
  "components.social Connection": "Conexión social",
  "components.sorryServerGoesWrong": "Lo siento, el servidor sale mal",
  "components.spaceUsed": "Espacio utilizado",
  "components.state": "Estado",
  "components.submit": "Entregar",
  "components.success": "Sucesión",
  "components.summary": "Resumen",
  "components.task_question": "Cuestionario",
  "components.tasklist": "Tarea",
  "components.tasklists": "Tareas",
  "components.title": "Título",
  "components.today": "Hoy",
  "components.totalPrice": "Precio total",
  "components.trash": "Papelera",
  "components.trending": "Tendencia",
  "components.unlock": "Descubrir",
  "components.username": "Nombre de usuario",
  "components.viewCart": "Ver coche de compras",
  "components.warning": "Advertencia",
  "components.week": "Semana",
  "components.withDescription": "Con descripción",
  "components.withHtml": "Con html",
  "components.year": "Año",
  "components.yesterday": "Ayer,",
  "components.zip": "Cremallera",
  "components.zipCode": "Cremallera",
  "config.append_questionnaires": "¿Adjuntar cuestionarios?",
  "config.checkin": "Realiza el check-in",
  "config.confirm": "Confirmación de participantes",
  "config.confirmation": "Confirmación de participantes",
  "config.custom_fields": "¿Quieres usar campos personalizados?",
  "config.customDomain": "Dominio personalizado",
  "config.event_check": "¿Quieres asociarte con un evento o campaña?",
  "config.hide_header": "Esconder el encabezado de la tienda",
  "config.hide_login": "Ocultar la barra de inicio de sesión de Qiplus",
  "config.hide_menu": "Ocultar el menú del sitio",
  "config.integrated": "Integrarse con otra plataforma",
  "config.liveqiplus": "Integrarse con Qiplus en vivo",
  "config.managers_mode": "Modo de distribución",
  "config.model_check": "Llenar de un modelo?",
  "config.notification": "Notificación",
  "config.notifications": "Notificaciones",
  "config.participants_score": "Puntuación por participación",
  "config.prevent_dup_ticket":
    "Evite que la automatización se viaje más de una vez en el mismo boleto",
  "config.prevent_dup_user":
    "Evite que la automatización sea viajada más de una vez por el mismo usuario",
  "config.questionnaire": "Tiene cuestionario",
  "config.raffle": "Celera de sorteo",
  "config.raffleType": "Tipo de dibujo",
  "config.redirect":
    "¿La redirección conduce a otro destino después del envío?",
  "config.repeat":
    "Permita que el correo electrónico se envíe más de una vez al mismo contacto",
  "config.sales_mode": "Modalidad de ventas",
  "config.score": "Soma anotación para participación",
  "config.sellers_mode": "Modo de distribución",
  "config.SMTPSettings": "Configuración SMTP",
  "config.stock_check": "Stock de control",
  "config.use_as_template": "Modelo",
  "contact.add": "Nuevo contacto",
  "contact.clone": "CONTACTO",
  "contact.create": "Crear contacto",
  "contact.edit": "Editar contacto",
  "contacts.clone": "Contactos de clonación",
  "contacts.edit": "Editar contactos",
  "contracts.defaultSubject": "Qiplus ||Contrato",
  "contracts.repeat":
    "Permita que el contrato se envíe más de una vez al mismo contacto",
  "customers.selectOneCustomer": "Seleccione un cliente",
  "date.showGroupFormat": "dddd, dd [de] mmmm [de] yyyy",
  "date.sortFullDate": "Yyyyymmddhmmss",
  "date.sortGroupFormat": "Yyyymmdd",
  "dates.after": "Después",
  "dates.before": "Antes",
  "dates.between": "Entre",
  "dates.currentMonth": "Mes actual",
  "dates.currentWeek": "Semana actual",
  "dates.currentYear": "Año corriente",
  "dates.custom": "Personalizado",
  "dates.dateFormat": "Formato",
  "dates.from": "De",
  "dates.fromAlt": "Desde",
  "dates.last_28d": "Últimos 28 días",
  "dates.last_3d": "Últimos 3 días",
  "dates.last_7d": "Últimos 7 días",
  "dates.last_90d": "Últimos 90 días",
  "dates.last_month": "Mes pasado",
  "dates.last_week_sun_sat": "La semana pasada",
  "dates.last_year": "El año pasado",
  "dates.last15Days": "Últimos 15 días",
  "dates.last30Days": "Últimos 30 días",
  "dates.last365Days": "Últimos 365 días",
  "dates.last7Days": "Últimos 7 días",
  "dates.last90Days": "Últimos 90 días",
  "dates.modified": "Modificado en",
  "dates.period": "Período",
  "dates.periodicity": "Periodicidad",
  "dates.this_month": "Este mes",
  "dates.this_week_sun_today": "Esta semana",
  "dates.this_year": "Este año",
  "dates.today": "Hoy",
  "dates.until": "Hasta",
  "dates.yesterday": "Ayer",
  "deals.createFunnelBeforeEdit":
    "Crear al menos un embudo para asociar negociaciones",
  "deals.info": "Datos comerciales",
  "deals.newContacTitle": "Contacto",
  "deals.sentTo.lost": "El negocio terminó como perdido",
  "deals.sentTo.won": "Negocio cerrado como ganancia",
  "deals.setNewContactData":
    "Se encontró un contacto en la base de datos con los datos ingresados.¿Quieres importarlo?",
  "domains.DNSInstructions":
    "Configure los servidores DNS en su proveedor de dominio con los siguientes valores:",
  "domains.subdomainInstructions":
    "Para configurar un subdominio, use esta dirección IP:",
  "email.content": "Cuerpo de correo electrónico",
  "email.defaultSubject": "Qiplus ||Correo electrónico",
  "email.error": "Error",
  "email.fields.address": "DIRECCIÓN",
  "email.fields.bcc": "CCO",
  "email.fields.cc": "CC",
  "email.fields.content": "Cuerpo de correo electrónico",
  "email.fields.forward": "Volver a conectar",
  "email.fields.from": "de",
  "email.fields.fromEmail": "Correo electrónico de remitente",
  "email.fields.fromName": "Remitente",
  "email.fields.host": "Anfitrión",
  "email.fields.html": "Mensaje",
  "email.fields.password": "Contraseña",
  "email.fields.port": "Puerta",
  "email.fields.provider": "Proveedor de correo electrónico",
  "email.fields.signature": "Firma",
  "email.fields.subject": "Sujeto",
  "email.fields.tls": "TLS (cifrado de seguridad)",
  "email.fields.to": "a",
  "email.fields.user": "Usuario (correo electrónico)",
  "email.head.bcc": "CCO",
  "email.head.cc": "CC",
  "email.head.from": "de",
  "email.head.subject": "Sujeto",
  "email.head.to": "a",
  "email.pending": "Pendiente",
  "email.processing": "Proceso",
  "email.scheduled": "Programado",
  "email.scheduledError": "El correo electrónico no se pudo programar",
  "email.scheduledSuccess": "Correo electrónico programado con éxito",
  "email.sending": "Envío",
  "email.sent": "Enviado",
  "email.sentError": "El correo electrónico no se pudo enviar",
  "email.sentSuccess": "Correo electrónico enviado correctamente",
  "email.test": "Prueba",
  "email.testSent": "Prueba presentada",
  "email.unknown": "Desconocido",
  "emails.attachment": "Anexo",
  "emails.attachments": "Accesorios",
  "emails.bcc": "CCO",
  "emails.broadcast": "Enviar a toda la base de plomo",
  "emails.cc": "CC",
  "emails.defaultFooter":
    "Forma de correo por correo electrónico predeterminado",
  "emails.defaultFooterTip":
    "Se puede reemplazar en la pantalla de edición de correo electrónico",
  "emails.defaultSendMethod": "Método de envío estándar",
  "emails.deleteAllScheduledMail": "¿Eliminar todos los envíos programados?",
  "emails.emailTestSuccess": "Prueba enviada con éxito",
  "emails.footer": "Pie de página por correo electrónico",
  "emails.footerAddress": "Dirección en el pie de página de correo electrónico",
  "emails.footerAddressExample":
    "Ej: Sistemas Qiplus.Rua Adélia de Oliveira, 30. Pacaembu residencial, itupeva - sp.CEP 13295-000.Brasil",
  "emails.footerAddressTip":
    "Ingrese su dirección completa de acuerdo con la Ley General de Protección de Datos (LGPD).",
  "emails.from": "Remitente",
  "emails.fromName": "Remitente",
  "emails.markAsSpam": "Correo basura",
  "emails.markedAsSpam": "Correos electrónicos marcados como spam",
  "emails.markedAsSpam.singular": "Correo electrónico marcado como spam",
  "emails.minDateMessage":
    "La fecha y hora de envío deben ser más altas que la fecha actual.",
  "emails.movedMsg": "Correos electrónicos conmovidos exitosos",
  "emails.movedMsg.singular": "Correo electrónico movido con éxito",
  "emails.moveMsg": "Mover correo electrónico",
  "emails.moveMsg.singular": "Mover correo electrónico",
  "emails.recipient": "Beneficiario",
  "emails.recipients": "Receptores",
  "emails.repeat":
    "Permita que el correo electrónico se envíe más de una vez al mismo contacto",
  "emails.scheduleBroadcast":
    "¿Estás seguro de que quieres enviar a toda tu base de plomo?",
  "emails.scheduled_date": "Fecha de envío",
  "emails.scheduledSuccess": "Correos electrónicos programados exitosos",
  "emails.scheduleEmail": "Escala",
  "emails.scheduleNewMail":
    "¿Quieres programar el correo electrónico tomado ahora?",
  "emails.selectMailbox": "Seleccione un cuadro de entrada",
  "emails.sendingOptions": "Opciones de envío",
  "emails.sendMethod": "Método de envío",
  "emails.sendNewMail": "¿Quieres enviar un correo electrónico ahora?",
  "emails.sentError": "No se pudieron enviar correos electrónicos",
  "emails.sentSuccess": "Correos electrónicos enviados correctamente",
  "emails.signinProvider": "Hacer inicio de sesión con su proveedor",
  "emails.signinProviderAndTyAgain":
    "Haga inicio de sesión con su proveedor y vuelva a intentarlo",
  "emails.subject": "Sujeto",
  "errors.birthMonth": "Seleccione un mes",
  "errors.chooseFieldType": "Seleccione el tipo de campo",
  "errors.createChoicesBeforeAddingField": "Crear opciones para el campo",
  "errors.dbErrorMsg":
    "Se produjo un error en la conexión de la base de datos.Intentar otra vez",
  "errors.emptyInput": "Complete todos los campos",
  "errors.errorCode": "Código de error",
  "errors.errorFetchingPosts":
    "Se produjo un error al cargar los elementos de esta sección.Intente nuevamente o comuníquese con nuestro soporte si el error persiste",
  "errors.failedValidation": "Errores correctos en el formulario",
  "errors.fetchErrorMsg":
    "Se produjo un error en la solicitud.Intentar otra vez",
  "errors.gender": "Seleccione un género",
  "errors.genericErrorMsg":
    "Se produjo un error inesperado.Intente nuevamente o comuníquese con nuestro soporte si el error persiste",
  "errors.inexistsErrorMsg": "El documento no existe en la base de datos",
  "errors.integrationErrorMsg":
    "Se produjo un error en la integración.Intente nuevamente o comuníquese con nuestro soporte si el error persiste",
  "errors.invalidCNPJ": "Número de CNPJ no válido",
  "errors.invalidCPF": "Número de CPF no válido",
  "errors.invalidEmailAddress": "El correo electrónico informado no es válido",
  "errors.invalidForwardAddress":
    "El correo electrónico para la redirección no es válido",
  "errors.invalidMonth": "El mes informado no es válido",
  "errors.invalidRecipients": "Receptor no válido",
  "errors.invalidZipCode": "Código postal no válido",
  "errors.loopDanger":
    "Esta acción no se puede realizar ya que genera un bucle infinito.",
  "errors.MODULE_LIMIT_REACHED":
    "Has alcanzado el límite de elementos de este módulo Qiplus.Realice una actualización de su cuenta para aumentar el límite",
  "errors.MODULE_NOT_AVAILABLE":
    "Este módulo no está habilitado.Realice una actualización de su cuenta para acceder a ella",
  "errors.permissionDenied":
    "Su usuario no tiene el permiso necesario para esta operación.",
  "errors.sessionExpired":
    "Su sesión de autenticación expiró, inicie sesión nuevamente",
  "errors.setFieldLabel": "Ingrese una etiqueta para el campo",
  "errors.userHasBeenRemoved": "El usuario no existe o no está activo",
  "errors.yearCantBeLowerThanCurrent":
    "El año debe ser mayor o igual a la corriente",
  "errors.invalidCustomToken": "Token inválido o expirado",
  "events.participants": "Participantes del evento",
  "facebook.facebookRecomendation":
    "Recomendamos que inicie sesión en la cuenta comercial, para que no haya conflictos",
  "facebook.logoutAlert":
    "Si baja, perderá todas las integraciones conectadas con esta cuenta de Facebook hasta que se conecte nuevamente a la aplicación Qiplus",
  "facebookcapi.access_token": "Token de acceso",
  "facebookcapi.AddPaymentInfo": "Agregar datos de pago",
  "facebookcapi.AddToCart": "añadir a la cesta",
  "facebookcapi.AddToWishlist": "Agregar a la lista de deseos",
  "facebookcapi.CompleteRegistration": "Concluir el registro",
  "facebookcapi.Contact": "Contacto",
  "facebookcapi.customEvent": "Evento personalizado",
  "facebookcapi.customEventName": "Nombre del evento personalizado",
  "facebookcapi.CustomizeProduct": "Personalizar el producto",
  "facebookcapi.description.AddPaymentInfo":
    "La adición de información de pago del cliente durante el proceso de finalización de la compra.Por ejemplo, cuando una persona hace clic en un botón para guardar información de facturación.",
  "facebookcapi.description.AddToCart":
    "La adición de un artículo al carrito o canasta de compras.Por ejemplo, haga clic en un botón Agregar al carrito en un sitio.",
  "facebookcapi.description.AddToWishlist":
    "La adición de elementos a la lista de deseos.Por ejemplo, hacer clic en un botón Agregar a la lista de deseos en un sitio.",
  "facebookcapi.description.CompleteRegistration":
    "Enviar información por parte de un cliente a cambio de proporcionar un servicio de empresa.Por ejemplo, registro para la firma de correo electrónico.",
  "facebookcapi.description.Contact":
    "Un teléfono, SMS, correo electrónico, chat u otro contacto entre un cliente y su negocio.",
  "facebookcapi.description.CustomizeProduct":
    "Personalización del producto a través de una herramienta de configuración u otra aplicación que tiene su empresa.",
  "facebookcapi.description.Donate":
    "La donación de fondos a su organización o causa.",
  "facebookcapi.description.FindLocation":
    "Cuando una persona encuentra una de sus ubicaciones en Internet, con la intención de visitar su establecimiento.Por ejemplo, investigue un producto y lo encuentre en una de sus tiendas locales.",
  "facebookcapi.description.InitiateCheckout":
    "El comienzo del proceso de acabado de compra.Por ejemplo, haga clic en el botón Finalizar el botón de compra.",
  "facebookcapi.description.Lead":
    "El envío de información por parte del cliente, sabiendo que la empresa puede contactarla más tarde.Por ejemplo, envíe un formulario o regístrese para una evaluación.",
  "facebookcapi.description.PageView":
    "Este es el píxel estándar que rastrea las visitas en la página.Por ejemplo, una persona accede a las páginas de su sitio",
  "facebookcapi.description.Purchase":
    "La finalización de una compra, generalmente indicada al recibir una confirmación de pedido, compra o recepción de la transacción.Por ejemplo, se dirija a una página de Acción de Gracias o confirmación.",
  "facebookcapi.description.Schedule":
    "Marcar un tiempo para visitar una de sus ubicaciones.",
  "facebookcapi.description.Search":
    "Una encuesta realizada en su sitio web, solicitud u otra propiedad.Por ejemplo, la investigación de productos o viajes.",
  "facebookcapi.description.StartTrial":
    "El inicio de la evaluación gratuita de un producto o servicio que ofrece.Por ejemplo, firma de evaluación.",
  "facebookcapi.description.SubmitApplication":
    "Enviar una solicitud a un producto, servicio o programa que ofrezca.Por ejemplo, tarjeta de crédito, programa educativo o empleo.",
  "facebookcapi.description.Subscribe":
    "El inicio de una suscripción paga con respecto a un producto o servicio que ofrece.",
  "facebookcapi.description.ViewContent":
    "Una visita a una página web importante para usted.Por ejemplo, una página de destino o página del producto.El contenido de visualización informa si alguien visitó la URL de una página web en particular sin informar qué hicieron o vieron en la página.",
  "facebookcapi.Donate": "Donar",
  "facebookcapi.eventContext": "Contexto de generación de eventos",
  "facebookcapi.eventName": "Evento",
  "facebookcapi.eventType": "Tipo de evento",
  "facebookcapi.externalEvents": "Eventos externos del sitio",
  "facebookcapi.FindLocation": "Encontrar ubicación",
  "facebookcapi.InitiateCheckout": "Comience a comprar acabado",
  "facebookcapi.Lead": "Registro",
  "facebookcapi.PageView": "Visualización de la página",
  "facebookcapi.pixel_id": "ID de píxel",
  "facebookcapi.Purchase": "Comprar",
  "facebookcapi.qiplusEvents": "Eventos de Qiplus",
  "facebookcapi.Schedule": "Cronograma",
  "facebookcapi.script": "Script para la página",
  "facebookcapi.Search": "Buscar",
  "facebookcapi.siteAction": "Acción del sitio",
  "facebookcapi.standardEvent": "Evento estándar",
  "facebookcapi.StartTrial": "Iniciar el período de evaluación",
  "facebookcapi.SubmitApplication": "Aplicar",
  "facebookcapi.Subscribe": "Para firmar",
  "facebookcapi.ViewContent": "Ver contenido",
  "facebookleads.clickToUnconnect": "Haga clic para desconectar",
  "facebookleads.connectAsAdmin":
    "Inicie sesión con el usuario administrador de su página de Facebook",
  "facebookleads.connectedAs": "Conectado como",
  "facebookleads.connectedWithLeadsAds": "Está conectado con qiplus",
  "facebookleads.connectedWithLeadsAdsInAnotherPost":
    "Está conectado con qiplus en otra integración",
  "facebookleads.connectLeadsAdsWith": "Conectar qiplus con",
  "facebookleads.connectPage": "Conéctese con la página",
  "facebookleads.connectWith": "Conectarse con",
  "facebookleads.connectYourPageToQIPlus":
    "Conecte su página con la aplicación Facebook Qiplus",
  "facebookleads.facebookConnectPages": "Conecte las páginas de Facebook",
  "facebookleads.loginbtn": "Iniciar sesión con Facebook",
  "facebookleads.loginbtnAs": "Haga que Facebook inicie sesión como",
  "facebookleads.logoutbtn": "Sal de Facebook",
  "facebookleads.noPagesAvailable":
    "No encontramos páginas asociadas con su usuario.Asegúrese de tener acceso al administrador a ellos e intente nuevamente",
  "facebookleads.page_access_token": "Acceso de token a su página de Facebook",
  "facebookleads.page_id": "ID de su página de Facebook",
  "facebookleads.qiplus_token": "Token Qiplus",
  "facebookleads.qiplus_verify_token": "Qiplus Check Token",
  "facebookleads.successfullyConnectedPage":
    "¡Felicidades!Conectaste tu página con la aplicación Facebook Qiplus",
  "facebookleads.successfullyUnconnectedPage":
    "Desconectó su página de la aplicación de Facebook Qiplus",
  "facebookleads.unconnectPageWarning":
    "“¿Desea desconectar su página de la aplicación QIPlus en Facebook? Sus leads ya no se enviarán desde su página a la plataforma QIPlus",
  "facebookleads.userConflictAlert":
    "Esta integración fue realizada por otro usuario de Facebook",
  "facebookleads.userConflictPlaceholder":
    "Iniciar sesión como [%s] para poder editar la integración",
  "facebookleads.userLoginAlert":
    "Hacer inicio de sesión con el usuario original para poder editar la integración",
  "fieldTypes.checkbox": "Casilla de verificación (opción múltiple)",
  "fieldTypes.date_picker": "Selector de fechas",
  "fieldTypes.email": "Correo electrónico",
  "fieldTypes.number": "Número",
  "fieldTypes.password": "Contraseña",
  "fieldTypes.radio": "Botones de radio (respuesta única)",
  "fieldTypes.select": "Selector (respuesta única)",
  "fieldTypes.text": "Texto simple",
  "fieldTypes.textarea": "Área de texto",
  "fieldTypes.url": "Url",
  "format.date": "Dd/mm/aaa",
  "format.date.full": "Dd/mm/aaa yyy hh: mm: ss",
  "format.date.full.short.year": "DD/MM/YY HH: MM: SS",
  "format.date.full.short.year.short.hour": "DD/MM/YY HH: MM",
  "format.date.short.month": "Mm/yyyy",
  "format.date.short.year": "Yyy",
  "forms.address": "DIRECCIÓN",
  "forms.addresses": "Direcciones",
  "forms.addressFields": "Campos de dirección",
  "forms.avatar": "Avatar",
  "forms.basicFields": "Campos básicos",
  "forms.birthday": "Fecha de nacimiento",
  "forms.button": "Botón",
  "forms.city": "Ciudad",
  "forms.cnpj": "CNPJ",
  "forms.comp": "Complementar",
  "forms.companyName": "Nombre de empresa",
  "forms.complementary": "Complementar",
  "forms.contact": "Contacto",
  "forms.contactId": "Contacto",
  "forms.contract": "Contrato",
  "forms.contractNumber": "Número de contrato",
  "forms.contracts": "Contrato",
  "forms.country": "País",
  "forms.countryCode": "País",
  "forms.cpf": "CPF",
  "forms.customField": "Campo personalizado",
  "forms.customFields": "Campos personalizados",
  "forms.dateAdded": "Agregado",
  "forms.dateFields": "Fechas",
  "forms.dateRegistered": "Fecha de registro",
  "forms.description": "Descripción",
  "forms.displayName": "Nombre de contacto",
  "forms.document": "Documento",
  "forms.document_number": "N de documento",
  "forms.document_type": "Tipo de documento",
  "forms.email": "Correo electrónico",
  "forms.facebook": "Facebook",
  "forms.field": "Campo",
  "forms.fieldBlocks": "Bloques de campo",
  "forms.fieldChoices": "Opciones de campo",
  "forms.fieldLabel": "Etiqueta de campo",
  "forms.fieldName": "Nombre de campo",
  "forms.fieldPlaceholder": "Fondo",
  "forms.fields": "Campos",
  "forms.fieldType": "Tipo de campo",
  "forms.fieldValue": "Valor de campo",
  "forms.firstName": "Nombre",
  "forms.formURL": "Forma URL",
  "forms.fullName": "Nombre completo",
  "forms.gender": "Género",
  "forms.gender.fem": "Femenino",
  "forms.gender.masc": "Masculino",
  "forms.genderSelect": "Seleccione el género",
  "forms.id": "IDENTIFICACIÓN",
  "forms.ID": "IDENTIFICACIÓN",
  "forms.instagram": "Instagram",
  "forms.lastName": "Apellido",
  "forms.linkedin": "LinkedIn",
  "forms.manager": "Gerente de cuentas",
  "forms.manager:email": "Correo electrónico del administrador de cuentas",
  "forms.manager:mobile": "Administrador de cuentas móvil",
  "forms.manager:name": "Nombre del administrador de la cuenta",
  "forms.manager:phone": "Teléfono del administrador de cuentas",
  "forms.managerEmail": "Correo electrónico del administrador de cuentas",
  "forms.managerMobile": "Administrador de cuentas móvil",
  "forms.managerName": "Nombre del administrador de la cuenta",
  "forms.managerPhone": "Teléfono del administrador de cuentas",
  "forms.maritalStatus": "Estado civil",
  "forms.maritalStatus.divorced": "Divorciado",
  "forms.maritalStatus.married": "Casado",
  "forms.maritalStatus.other": "Otro",
  "forms.maritalStatus.single": "Soltero",
  "forms.maritalStatus.widowed": "Viudo",
  "forms.mobile": "Teléfono móvil",
  "forms.mobileWithCC": "Teléfono celular con código de país y DDD",
  "forms.modified": "Modificado",
  "forms.name": "Nombre",
  "forms.names": "Nombres",
  "forms.neighborhood": "Vecindario",
  "forms.newField": "Nuevo campo",
  "forms.nickname": "Nombre de usuario",
  "forms.num": "Número",
  "forms.occupation": "Profesión",
  "forms.optionAdd": "Añadir opción",
  "forms.optionLabel": "Etiqueta de opción",
  "forms.optionValue": "Valor de opción",
  "forms.other": "Otros",
  "forms.password": "Contraseña",
  "forms.paymentDetails": "Detalles de pago",
  "forms.paymentMethod": "Forma de pago",
  "forms.personalize": "Personalizar",
  "forms.phone": "Teléfono",
  "forms.postalCode": "Cremallera",
  "forms.professional": "Profesional",
  "forms.professionals": "Profesionales",
  "forms.profile": "Perfil",
  "forms.promoter": "Promotor",
  "forms.promoter:email": "Correo electrónico del fiscal",
  "forms.promoter:mobile": "Fiscal",
  "forms.promoter:name": "Fiscal",
  "forms.promoter:phone": "Teléfono del fiscal",
  "forms.promoterEmail": "Correo electrónico del fiscal",
  "forms.promoterMobile": "Fiscal",
  "forms.promoterName": "Fiscal",
  "forms.promoterPhone": "Teléfono del fiscal",
  "forms.pts": "Puntaje",
  "forms.pts_gained": "Puntaje generado",
  "forms.qiFields": "Qi Plus Fields",
  "forms.qrcode": "Código QR",
  "forms.qualification": "Calificación",
  "forms.readonly": "Solo lectura",
  "forms.redirect":
    "¿Redirect conduce a otro destino después de enviar el formulario?",
  "forms.ref": "Referencia",
  "forms.required": "Obligatorio",
  "forms.requiredFields": "Campos obligatorios",
  "forms.rg": "Rg",
  "forms.saveCustomField": "Guardar en la base de datos",
  "forms.score": "Puntaje",
  "forms.score_gained": "Puntaje generado",
  "forms.select.birthdayMonth": "Aniversario",
  "forms.seller": "Vendedor",
  "forms.seller:email": "Correo electrónico del vendedor",
  "forms.seller:mobile": "Vendedor celular",
  "forms.seller:name": "Vendedor",
  "forms.seller:phone": "Teléfono del vendedor",
  "forms.sellerEmail": "Correo electrónico del vendedor",
  "forms.sellerMobile": "Vendedor celular",
  "forms.sellerName": "Vendedor",
  "forms.sellerPhone": "Teléfono del vendedor",
  "forms.social": "Redes sociales",
  "forms.socialFields": "Campos de redes sociales",
  "forms.socialNewtork": "Red social",
  "forms.socialNewtorks": "Redes sociales",
  "forms.stage": "Paso",
  "forms.state": "Estado",
  "forms.street": "Camino",
  "forms.street_number": "Número",
  "forms.submitConfirmPage": "Página de confirmación",
  "forms.tags": "Etiquetas",
  "forms.team": "Equipo",
  "forms.thankYouMessage": "Mensaje de confirmación",
  "forms.ticket": "Orden",
  "forms.ticketItems": "Ordenar artículos",
  "forms.ticketNumber": "Número de orden",
  "forms.tickets": "Solicitudes",
  "forms.title": "Título",
  "forms.twitter": "Gorjeo",
  "forms.type": "Tipo de contacto",
  "forms.type.corporation": "Empresa",
  "forms.type.individual": "Individual",
  "forms.updatedAt": "Modificado en",
  "forms.url": "Sitio web",
  "forms.zipcode": "Cremallera",
  "funnels.cartMode": "Cesta de la compra",
  "funnels.contactedStage": "Contactado",
  "funnels.negotiatingStage": "Negociación",
  "funnels.proposedStage": "Propuesta celebrada",
  "funnels.prospectStage": "Prospección",
  "funnels.singleProductMode": "Producto único",
  "funnels.singleValueMode": "Valor cerrado",
  "funnels.tooltip.actions":
    "Monte su plan aquí, cuáles serán los pasos de la automatización deseada de cada paso de su cliente.",
  "funnels.tooltip.create": "Comience el viaje de su cliente.",
  "funnels.tooltip.goal":
    "Los objetivos predefinidos se mostrarán en la tubería de vendedores/gerentes.",
  "funnels.tooltip.pipeline":
    "Establezca los pasos de ventas y las tareas de su equipo.\\ n Perdido: negocios perdidos.\\ n ganancias: ganancias comerciales.",
  "funnels.tooltip.sales_mode":
    "El modo de ventas afectará el valor del negocio, así como la forma en que el vendedor interactúa con la tarjeta de presentación.\\ n Canasta de compra: el vendedor selecciona qué productos compró el cliente, y genera un boleto de venta a precios de acuerdo con el valor de los productos seleccionados.\\ n negociado: el vendedor inserta el monto acordado con el cliente.\\ n Valor cerrado: todas las empresas están cerradas con el valor predefinido.",
  "funnels.tooltip.shotx": "Elija las instancias disponibles",
  "funnels.tooltip.store":
    "Campo opcional.Se recomienda agregar tiendas para segmentar el origen de las compras.Ej: Empresas que tienen más de una unidad.",
  "funnels.tooltip.title":
    "Escriba el título de su embudo de ventas.\\ n ex: captura de plomo de la página de destino.",
  "funnels.valuesMode": "Valor negociado",
  "goals.checkin": "Registrarse",
  "goals.confirmed": "Participantes confirmados",
  "goals.daily": "A diario",
  "goals.final": "Fin",
  "goals.monthly": "Mensual",
  "goals.participants": "Participantes",
  "goals.registers": "Archivos",
  "goals.views": "Vistas",
  "goals.weekly": "Semanalmente",
  "googleads.loginAsAdmin": "Buscar en los anuncios de Google",
  "hint.searchMailList": "Buscar correos electrónicos",
  "hint.whatAreYouLookingFor": "Qué estás buscando",
  "icon.stock": "Ti-server",
  "import.updateData": "Actualizar registros existentes",
  "instructions.acountInvestments":
    "Ingrese las inversiones realizadas (título, valor, periodicidad) para que podamos calcular el retorno de las métricas de inversión",
  "instructions.investmentQuickField":
    "Puede usar este campo para simular métricas de retorno de inversión, incluso si ha informado en sus configuraciones las inversiones realizadas",
  "instructions.investments":
    "Ingrese las inversiones realizadas (título y valor) para obtener las métricas de retorno de la inversión",
  "instructions.leaveBlankForModel": "Que se use solo como modelo",
  "integrations.addTags": "Agregar etiquetas",
  "integrations.apiKey": "Llave de API",
  "integrations.apiUrl": "URL API",
  "integrations.appID": "ID de aplicación",
  "integrations.associateCustomFields": "Campos personalizados",
  "integrations.bluesoft": "Bluesoft",
  "integrations.customSMTP": "Propio SMTP",
  "integrations.default": "Por defecto",
  "integrations.destinationUrl": "URL de destino",
  "integrations.eduzz": "Hacer de comer",
  "integrations.elementor": "Elemento",
  "integrations.emailField": "Campo de correo electrónico",
  "integrations.encriptation": "Tipo de finalización",
  "integrations.facebookads.selectCampaign": "Seleccione una campaña",
  "integrations.fieldAssociated": "Campo asociado",
  "integrations.fieldIDOnSource": "ID o nombre de campo en la plataforma",
  "integrations.fieldToAssociate": "Campo asociado",
  "integrations.form": "Formularios",
  "integrations.formID": "Formulario de identificación",
  "integrations.g_calendar": "Calendario de Google",
  "integrations.gcalId": "ID de calendario",
  "integrations.gformsFunction": "Función para el script de formularios",
  "integrations.gmail": "Gmail SMTP",
  "integrations.googleads.connect": "Conecte los anuncios de Google",
  "integrations.googleads.customerDescription": "Cliente descrito",
  "integrations.googleads.customerId": "ID de cliente",
  "integrations.googleads.customers": "ADS de Google - Clientes",
  "integrations.googleads.customersInstructions":
    "Ingrese a los clientes de Google ADS aquí que administre.",
  "integrations.googleads.getReport": "Informe",
  "integrations.googleforms": "Formularios de Google",
  "integrations.googleformsCollectEmail":
    "El formulario está configurado para recopilar correos electrónicos",
  "integrations.hotmart": "Hotmart",
  "integrations.imap": "Integración IMAP",
  "integrations.imapSettings": "Configuración de entrada IMAP",
  "integrations.importTags": "Etiquetas de importación",
  "integrations.importTags.instructions":
    "Importar etiquetas de plomo en la plataforma original, cuando esté disponible",
  "integrations.infusionsoft": "Infusión",
  "integrations.leadlovers": "Amantes de los principales",
  "integrations.mailDomainActive": "Tu dominio está activo",
  "integrations.mailDomainAlreadyExists":
    "Domain [%Domain] ya está configurado en una integración.",
  "integrations.mailDomainCheck": "Verificar",
  "integrations.mailDomainChecking": "Verificación de dominio",
  "integrations.mailDomainInactive":
    "Su dominio no está configurado, por favor verifique",
  "integrations.mailDomainInstruction":
    "Visite el proveedor de DNS que usa para administrar <b> [%de dominio] </b> y agregue los siguientes registros DNS.",
  "integrations.mailDomainMensagem":
    "Recomendamos usar el subdomen de correo para hacer la configuración.Ej: mail.meudominio.com",
  "integrations.mailDomainName": "Remitente",
  "integrations.mailDomainPersonalized": "Correo electrónico personalizado",
  "integrations.mandeumzap": "Enviar un shotx",
  "integrations.mautic": "Mautico",
  "integrations.nameGeneric": "Nombre de webhok",
  "integrations.notazz": "Notazz",
  "integrations.optionGeneric": "Agrupación de webhook",
  "integrations.phoneNumber": "Número de teléfono",
  "integrations.priorityRecords": "DNS recibiendo prioridad",
  "integrations.rdstation": "Estación de rd",
  "integrations.recordSendValue": "Tipo de envío Tipo",
  "integrations.recordsValueDNS": "Valor de grabación",
  "integrations.recordType": "Tipo de grabación",
  "integrations.reports.configure": "Configurar",
  "integrations.reports.noIntegrations": "No hay integración configurada",
  "integrations.reports.selectIntegrationAndAccount":
    "Selecciona una integración y una cuenta",
  "integrations.reports.selectIntegrationAndCustomer":
    "Seleccione una integración y un cliente",
  "integrations.selectOneIntegration": "Seleccione una integración",
  "integrations.senderEmail": "Correo electrónico de remitente",
  "integrations.senderName": "Remitente",
  "integrations.senderPass": "Remitente",
  "integrations.sendNameDNS": "Nombre de envío",
  "integrations.sendValue": "Valor de envío",
  "integrations.shopify": "Shop",
  "integrations.smtp": "Integración SMTP",
  "integrations.smtpHost": "Anfitrión SMTP",
  "integrations.smtpPort": "Puerta smtp",
  "integrations.smtpSettings": "Configuración de salida SMTP",
  "integrations.tags": "Etiquetas",
  "integrations.tags.instructions":
    "Indique las etiquetas que desea asignar a los contactos agregados a través de esta integración.",
  "integrations.webhookURL": "URL webhook",
  "integrations.woocommerce": "WooCommerce",
  "integrations.yourMailDomain": "Tu dominio",
  "interactions.module.singular": "interacción",
  "interactions.module.title": "Interacciones",
  "item.add": "Nuevo artículo",
  "item.clone": "Artículo cloner",
  "item.create": "Crear elemento",
  "item.edit": "Elemento de edición",
  "items.clone": "Elementos clon",
  "items.edit": "Editar elementos",
  "items.price": "Valor",
  "items.product": "Producto",
  "items.qty": "Unidades",
  "leads.addedToSegmentation": "Cables agregados a la segmentación",
  "leads.basicInfo": "Datos básicos",
  "leads.corporation": "Empresa",
  "leads.deleteConfirmationBody": "El plomo será eliminado permanentemente",
  "leads.deleteConfirmationTitle":
    "¿Estás seguro de que quieres eliminar este liderazgo?",
  "leads.dupLead": "Ya hay una ventaja con estos datos",
  "leads.individual": "Individual",
  "leads.switchLead": "¿Quieres editar el liderazgo?",
  "leads.type": "Tipo de contacto",
  "logs.noLogsYet": "No hay registros por ahora",
  "logs.viewAllLogFields": "Ver todo",
  "logs.viewGroupedLogFields": "Ver resumen",
  "mail.errors.emailTestFailed":
    "Se produjo un error al enviar el correo electrónico de prueba",
  "mail.errors.emptyFromName": "Complete el nombre del remitente",
  "mail.errors.emptySendMethod": "Elija el método de envío",
  "mail.errors.emptySubject": "Complete el campo de sujeto",
  "mail.errors.errorSendingEmail":
    "Se produjo un error al enviar el correo electrónico",
  "mail.errors.errorSendingEmails":
    "Se produjo un error al enviar el correo electrónico (s)",
  "mail.errors.invalidBody":
    "El contenido de correo electrónico no puede ser nulo",
  "mail.errors.invalidFromName": "El nombre del remitente no puede ser nulo",
  "mail.errors.invalidRecipients": "Receptor no válido",
  "mailboxes.authenticateInGmail": "Autenticar su cuenta de Gmail",
  "mailboxes.availableMailboxes":
    "Puede usar cualquiera de estas direcciones disponibles:",
  "mailboxes.confirmCloudMailboxCreation":
    "Se creará un nuevo cuadro de entrada y el correo electrónico para el envío no se puede cambiar más adelante a esta bandeja de entrada",
  "mailboxes.confirmCreation": "Confirme la creación de su bandeja de entrada",
  "mailboxes.draft": "Rascar",
  "mailboxes.IMAP": "IMAP - Dominio propio",
  "mailboxes.inbox": "Bandeja de entrada",
  "mailboxes.labels.CATEGORY_FORUMS": "Foros",
  "mailboxes.labels.CATEGORY_PERSONAL": "Personal",
  "mailboxes.labels.CATEGORY_PROMOTIONS": "Promociones",
  "mailboxes.labels.CATEGORY_SOCIAL": "Social",
  "mailboxes.labels.CATEGORY_UPDATES": "Actualizaciones",
  "mailboxes.labels.CHAT": "Charlar",
  "mailboxes.labels.DRAFT": "Bosquejo",
  "mailboxes.labels.IMPORTANT": "Importante",
  "mailboxes.labels.INBOX": "Bandeja de entrada",
  "mailboxes.labels.READ": "Leer",
  "mailboxes.labels.SENT": "Aroma",
  "mailboxes.labels.SPAM": "Correo basura",
  "mailboxes.labels.STARRED": "Estrellado",
  "mailboxes.labels.trash": "Papelera",
  "mailboxes.labels.TRASH": "Papelera",
  "mailboxes.labels.UNREAD": "No amado",
  "mailboxes.labels.UNSTARRED": "Sin estrellas",
  "mailboxes.sent": "Adjunto",
  "mailboxes.spam": "Correo basura",
  "mailboxes.trash": "Papelera",
  "mailboxes.unavailableEmailAddress":
    "La dirección de correo electrónico no está disponible",
  "managers.roulette": "Gerentes",
  "menu.account": "Mi cuenta",
  "menu.app": "Aplicación",
  "menu.campaigns": "Campañas",
  "menu.contact": "Contacto",
  "menu.contacts": "Contactos",
  "menu.dashboard": "Panel",
  "menu.deals": "Negocio",
  "menu.hunters": "QI Hunter",
  "menu.import": "Importar",
  "menu.managers": "Gerentes",
  "menu.model": "Modelo",
  "menu.models": "Modelos",
  "menu.myProfile": "Mi perfil",
  "menu.reports": "Informes",
  "menu.segmentationsGroup": "Segmentación",
  "menu.sellers": "Vendedor",
  "menu.tags": "Etiquetas",
  "menu.teams": "Equipos",
  "menu.tools": "Herramientas",
  "menu.tooltip.dashboard": "Vea aquí el desarrollo de su negocio",
  "menu.users": "Usuarios",
  "message.shotx.none": "No hay unax configurada",
  "messagesBroadcast.changed": "Mensaje de masa actualizado con éxito",
  "messagesBroadcast.error": "Algo sucedió al actualizar el mensaje masivo",
  "messagesBroadcast.module.description": "Enviar mensajes de masa",
  "messagesBroadcast.module.title": "Mensajes de masa",
  "messagesBroadcast.schedule.error":
    "Algo sucedió al programar el envío de mensajes masivos",
  "messagesBroadcast.schedule.success":
    "Envío de mensajes de masa correctamente",
  "messagesBroadcast.save.success":
    "Broadcast guardado con éxito",
  "messagesBroadcast.send.error": "Algo sucedió al enviar un mensaje masivo",
  "modal.banner.buttonPrimary": "Comprar shotx",
  "modal.banner.buttonSecondary": "Cancelar",
  "modal.banner.checkedNoShowAgain": "No mostrar",
  "modal.banner.title": "Conocerx",
  "modal.cancel": "Cancelar",
  "modal.close": "Para cerrar",
  "modal.export": "Exportar",
  "modal.export.completed": "Exportar todas las columnas",
  "modal.export.csv": "Exportar a CSV",
  "modal.export.resumed": "Exportar columnas principales",
  "modal.export.xlsx": "Exportar a XLSX",
  "modal.title": "Registros de exportación",
  "module.api": "API",
  "module.backend": "Backend",
  "module.drafts": "Rascar",
  "module.frontend": "Borde",
  "module.inbox": "Bandeja de entrada",
  "module.issue": "Asunto",
  "module.sent": "Adjunto",
  "module.spam": "Correo basura",
  "module.trash": "Papelera",
  "modules.app.name": "Qiplus",
  "modules.quick_messages": "Mensajes rápidos",
  "modules.quick_messages.added": "Mensaje rápido creado",
  "modules.quick_messages.changed": "Mensaje rápido actualizado",
  "modules.quick_messages.removed": "Mensaje rápido eliminado",
  "modules.quick_messages.short": "Mensaje rápido",
  "modules.quick_messages.singular": "Mensaje rápido",
  "nf.aliquotas": "Alícuota",
  "nf.apiError": "Se produjo un error al generar la factura",
  "nf.cnae": "Cienal",
  "nf.competence": "Período facturado",
  "nf.customer": "Cliente",
  "nf.goal": "Objetivo",
  "nf.ie": "Registro estatal",
  "nf.im": "Registro municipal",
  "nf.iss_check": "ISS retenido en la fuente",
  "nf.issue_date": "Fecha de emisión",
  "nf.LC116": "Artículo de lista de servicios LC 116",
  "nf.loadingMessage": "Generación de factura",
  "nf.nature_operation": "",
  "nf.operation_type": "Tipo de operación",
  "nf.product": "Producto",
  "nf.referenced": "Factura referenciada",
  "nf.service": "Servicio",
  "nf.service_code": "Código de servicio municipal",
  "nf.service_description": "Descripción del servicio municipal",
  "nf.successMessage": "¡Factura generada con éxito!",
  "nf.taxtype": "Tipo de persona",
  "nf.title": "Factura",
  "nf.type": "Tipo de factura",
  "nf.type_product": "Productos",
  "nf.type_service": "Servicios",
  "notification.check": "Notificación",
  "notification.interval": "",
  "notification.qty": "",
  "notifications.check": "Notificaciones",
  "notifications.emptyMsg": "Sin advertencia por ahora",
  "pagarme.openDashboard": "Ver en el tablero de pagarm",
  "pagarme.openManageUrl": "Editar con Pagarm",
  "pagarme.openPendingUpdate": "Ver actualización en el pago",
  "participants.checkin": "Check-in confirmado",
  "participants.checkinTime": "Registrado",
  "participants.confirm": "Confirmar el participante",
  "participants.confirmed": "Participante confirmado",
  "participants.confirmedTime": "Confirmado en",
  "participants.disconfirm": "Desconfianza Participante",
  "participants.disconfirmed": "Desconfianza Participante",
  "participants.undoCheckin": "Check-in cancelado",
  "payment.accountDoesNotExist": "La cuenta no existe o no está activa",
  "payment.accountHasPendingUpdates":
    "Esta cuenta tiene actualizaciones pendientes",
  "payment.accountIsNotActive": "OPS, parece que su cuenta no está activa",
  "payment.accountType": "Tipo de cuenta",
  "payment.activeSubscriptionAlert":
    "Ya hay una firma activa para esta cuenta.Cancelar la suscripción antes de generar un nuevo",
  "payment.agencia": "Agencia",
  "payment.agencia_dv": "Agencia DV",
  "payment.bankAccount": "Cuenta bancaria",
  "payment.bankCode": "Código bancario",
  "payment.billing": "Cargar",
  "payment.billingAccountNotFound":
    "Todavía no tiene una cuenta activa en Qi Plus.Cree su cuenta ahora o comuníquese con nosotros para aprender más sobre Qiplus.",
  "payment.boleto": "Bancos",
  "payment.cancelPendingUpdates": "Cancelar firmas pendientes",
  "payment.cancelSubscription": "Cancelar suscripción",
  "payment.cancelSubscriptionError": "Se produjo un error al cancelar la firma",
  "payment.cardCustomerName": "Nombre en la tarjeta",
  "payment.cash": "Pago al contado",
  "payment.checkout": "Verificar",
  "payment.checkoutSuccess": "El pago se mantiene con éxito",
  "payment.commission": "Comisión",
  "payment.commissions": "Comisiones",
  "payment.confirmCancelSubscription":
    "¿Estás seguro de que quieres cancelar la firma?Esta acción no se puede revertir más tarde",
  "payment.confirmCheckout": "Verificar",
  "payment.confirmPayment": "Confirmar el pago",
  "payment.conta": "Número de cuenta",
  "payment.conta_dv": "Cuenta DV",
  "payment.credit_card": "Tarjeta de crédito",
  "payment.delivery": "Entrega",
  "payment.delivery_fee": "Entrega",
  "payment.discount": "Descuento",
  "payment.discounts": "Descuentos",
  "payment.document_number": "Documento (CPF o CNPJ)",
  "payment.extras": "Pitty",
  "payment.gateway": "Forma de pago",
  "payment.gateway_type": "Medios de recolección",
  "payment.generateBoleto": "Generar boleto de pago",
  "payment.implementation": "Implementación",
  "payment.implementationAlreadyCharged":
    "Implementación ya cobrada en la primera firma",
  "payment.implementationCost": "Costo de implementación",
  "payment.installment": "Entrega",
  "payment.installments": "Entregas",
  "payment.legal_name": "Titular",
  "payment.maxInstallments": "Al",
  "payment.mercadoPagoCredentials": "Credenciales de mercado pagadas",
  "payment.noMonthlySubscription": "El plan no tiene una firma mensual",
  "payment.oneTimePayment": "vista",
  "payment.other_discounts": "Otros descuentos",
  "payment.other_fees": "Otras tarifas",
  "payment.other_taxes": "Otros impuestos",
  "payment.paymentLink": "Enlace de pago",
  "payment.planDetails": "Detalles del plan",
  "payment.planValue": "Valor del plan",
  "payment.pleaseCheckYourEmail":
    "Consulte su correo electrónico para acceder al enlace de pago o cambiar el método de pago",
  "payment.price": "Precio",
  "payment.prices": "Precios",
  "payment.printYourBoleto": "Imprima su boleto para realizar el pago",
  "payment.pts": "Puntos de Qi-Fastamente",
  "payment.recipient": "Beneficiario",
  "payment.recurrency": "Reaparición",
  "payment.secureCheckout": "Compra 100% seguro",
  "payment.startingAt": "De",
  "payment.subscription": "Firma",
  "payment.taxes": "Impuestos",
  "payment.ticketValue": "Solicitud total",
  "payment.total": "Total",
  "payment.totals": "Total",
  "payment.updatesWillBeReadyOnPaymentConfirm":
    "La actualización de la cuenta se activará automáticamente después de la confirmación de pago",
  "payment.upgrade": "Mejora",
  "payment.validThru": "válido",
  "payment.value": "Valor",
  "payment.values": "Valores",
  "payment.viaCreditCard": "en la tarjeta",
  "payment.yourAccountWillBeReadyOnPaymentConfirm":
    "Su cuenta estará activa automáticamente después de la confirmación de pago",
  "payment.yourAccountWillBeReadySoon":
    "Su cuenta estará activa en algunos momentos",
  "pipeline.addOneStage": "Agregar un paso",
  "pipeline.addStage": "Agregar paso",
  "pipeline.createSegmentation": "Crear segmentación",
  "pipeline.dstStage": "Etapa de destino",
  "pipeline.options": "Opciones de tubería",
  "pipeline.orderby": "Clasificar",
  "pipeline.orderByMensages": "Número de mensajes no advocables",
  "pipeline.orderByModified": "Fecha de modificación",
  "pipeline.orderByScoring": "Tanteo",
  "pipeline.removeOneStage": "Eliminar un paso",
  "pipeline.removeStage": "Eliminar el paso",
  "pipeline.stage": "Paso",
  "pipeline.stages": "Etapas",
  "placeholders.confirmOwnerUpdate": "¿Quiere actualizar la cuenta a [%s]?",
  "placeholders.confirmRemoval": "¿Quieres eliminar [%s]?",
  "placeholders.confirmUpdate": "¿Quieres actualizar [%s]?",
  "placeholders.create": "Crear [%s]",
  "placeholders.customMailNotValid":
    "El remitente no pertenece al dominio personalizado",
  "placeholders.email": "Correo electrónico",
  "placeholders.errorFetchingPosts":
    "Se produjo un error al cargar [%s].Intentar otra vez",
  "placeholders.errorTrashingPosts":
    "Se produjo un error eliminando [%s].Intentar otra vez",
  "placeholders.errorUpdatingPosts":
    "Se produjo un error actualizando [%s].Intentar otra vez",
  "placeholders.events.checkin": "Verificar a los participantes de [%A]",
  "placeholders.events.participants": "Participantes de [%s]",
  "placeholders.inexistsErrorMsg": "[%s] no existe en la base de datos",
  "placeholders.invalidField": "El campo [%s] no es válido",
  "placeholders.logoutAlert":
    "Debe iniciar sesión en su cuenta corriente [%s] para conectar una nueva cuenta",
  "placeholders.maxEmailsError":
    "La cantidad máxima de correo electrónico al campo [%s] es [%máximo]",
  "placeholders.maxValueError":
    "El valor máximo para el campo [%s] es [%máximo]",
  "placeholders.minEmailsError":
    "La cantidad mínima de correo electrónico al campo [%s] es [%min]",
  "placeholders.minRowsError": "Seleccione al menos [%min] [%s]",
  "placeholders.minValueError": "El valor mínimo para el campo [%s] es [%min]",
  "placeholders.MODULE_LIMIT_AVAILABLE":
    "El límite restante de [%s] en su cuenta es [%N]",
  "placeholders.MODULE_LIMIT_REACHED":
    "Has alcanzado el límite [%s] de su cuenta.Hacer una actualización para aumentar el límite",
  "placeholders.noGoalsFound":
    "Todavía no ha cargado la información objetivo en [%s].",
  "placeholders.noInvestmentsFound":
    "Todavía no ha cargado información de inversión en [%s].",
  "placeholders.noPostsFound": "No hay [%s] en su cuenta",
  "placeholders.noResultsFound": "No hay [%s] que coincidan con su búsqueda",
  "placeholders.noUsersFound": "No hay [%s] en su cuenta",
  "placeholders.password": "Contraseña",
  "placeholders.requiredField": "El campo [%s] es obligatorio",
  "placeholders.select": "Seleccionar ...",
  "placeholders.selectAnItem": "Seleccione [%s]",
  "placeholders.SMTP_SETTINGS_CONFIRM_EMAAIL":
    '<P> <img alt = "" src = "https://qiplus.com.br/ckfiles/i9ttrqsinnavr0f7x4udbtk6smy2/images/template-so-600x200.jpg" estilo = "altura: 200px; width: 600px"/> </p> <p> & nspsp; </p> <p> Felicitaciones, [%s]!',
  "placeholders.thankYouMessageLabel": "¡Gracias!",
  "placeholders.updateCampaignSettings":
    "Complete la configuración de la campaña para obtener acceso a los informes completos de Qiplus.",
  "placeholders.updateSettings":
    "Complete la configuración [%s] para acceder a los informes de Qiplus completos.",
  "policy.cookiesAcceptanceEntries":
    "Ver registros de la política de aceptación de las cookies",
  "policy.cookiesPolicy": "Política de cookies",
  "preferences.currency": "BRL",
  "preferences.currencySymbol": "R $",
  "preferences.locale": "PT-Br",
  "privacy.privacyAcceptanceEntries":
    "Vea los registros de la Política de aceptación de la privacidad",
  "privacy.privacyPolicy": "política de privacidad",
  "privacy.privacyScript":
    "Script para la aceptación de la política de privacidad",
  "privacy.privacyText": "Texto de la política de privacidad",
  "privacy.privacyType": "Contenido de la política de privacidad",
  "privacy.privacyUrl": "URL de política de privacidad",
  "privacy.termsAcceptanceEntries":
    "Consulte los registros de aceptación de los términos de uso",
  "privacy.termsOfUse": "CONDICIONES DE USO",
  "privacy.termsScript": "Script para la aceptación de los términos de uso",
  "privacy.termsText": "Texto de los términos de uso",
  "privacy.termsType": "Contenido de los Términos de uso",
  "privacy.termsUrl": "URL de términos de uso",
  "qiusers.corporation": "Empresa",
  "qiusers.deleteConfirmationBody": "El usuario será eliminado permanentemente",
  "qiusers.deleteConfirmationTitle":
    "¿Estás seguro de que quieres eliminar a este usuario?",
  "qiusers.dupLead": "Ya hay un usuario con estos datos",
  "qiusers.individual": "Individual",
  "qiusers.switchLead": "¿Quieres editar al usuario?",
  "qiusers.type": "Tipo de usuario",
  "questionnaires.answer": "Respuesta",
  "questionnaires.answers": "Respuestas",
  "questionnaires.question": "Pregunta",
  "questionnaires.questions": "Preguntas",
  "questionnaires.score": "Puntaje",
  "questionnaires.scores": "Montones",
  "quickMessages.add": "Nuevo mensaje",
  "quickMessages.cancel": "Cancelar",
  "quickMessages.create": "Crear mensaje",
  "quickMessages.edit": "Mensaje de edición",
  "quickMessages.enabled": "Experto",
  "quickMessages.enabledOnAutomation": "Automatización",
  "quickMessages.enabledOnQuickMessage": "Mensajes rápidos",
  "quickMessages.message": "Mensaje",
  "quickMessages.module.singular": "Mensaje rápido",
  "quickMessages.module.title": "Mensajes rápidos",
  "quickMessages.module.tooltip": "Mensajes rápidos para conversaciones",
  "quickMessages.no": "No",
  "quickMessages.releaseOn": "Disponible",
  "quickMessages.tooltipAutomation":
    "Estará disponible para la selección de automataciones de shotx",
  "quickMessages.tooltipShotx":
    "Estará disponible para enviar mensajes con un solo clic a un negocio a través de Shotx",
  "quickMessages.vars": "Variables",
  "quickMessages.vars.clientName": "Nombre del cliente",
  "quickMessages.vars.vendorName": "Vendedor",
  "quickMessages.yes": "Sí",
  "redirect.customUrl": "URL personalizada",
  "redirect.qiplusPage": "Página de Qiplus",
  "repeater.removeOneItem": "Eliminar un artículo",
  "reports.accepted": "Aceptado",
  "reports.accepted.singular": "Aceptado",
  "reports.ads": "anuncios",
  "reports.ageRangeError":
    "El intervalo de edad no es válido, el comienzo debe ser menor que el final",
  "reports.clicked": "Hecho clic",
  "reports.clicked.singular": "Hecho clic",
  "reports.clickLink": "Haga clic en el enlace",
  "reports.clicks": "Hacer clics",
  "reports.complained": "Quejas",
  "reports.complained.singular": "Queja",
  "reports.conversionsValue": "Conversiones",
  "reports.cost": "Costo",
  "reports.costMicros": "Costo",
  "reports.costs": "Costo",
  "reports.cpc": "Costo por clic",
  "reports.ctr": "CTR",
  "reports.delivered": "Entregado",
  "reports.delivered.singular": "Entregado",
  "reports.engagement": "Compromiso",
  "reports.error.singular": "No invitado",
  "reports.errorMsg":
    "Seleccione una página o redefinir el intervalo de fechas",
  "reports.failed": "No entregado",
  "reports.failed.singular": "No invitado",
  "reports.followers": "Seguidores",
  "reports.frequency": "Frecuencia",
  "reports.hardBounces": "Rebote duro",
  "reports.impressions": "Impresiones",
  "reports.interactions": "Interacciones con contenido",
  "reports.interactions": "Interacciones",
  "reports.interactionsTax": "Tasa de interacción",
  "reports.last_28d": "Últimos 28 días",
  "reports.last_3d": "Últimos 3 días",
  "reports.last_7d": "Últimos 7 días",
  "reports.last_90d": "Últimos 90 días",
  "reports.last_month": "Mes pasado",
  "reports.last_week_sun_sat": "La semana pasada",
  "reports.last_year": "El año pasado",
  "reports.metrics": "Métrica",
  "reports.noData": "No hay datos para mostrar",
  "reports.opened": "Abierto",
  "reports.opened.singular": "Abierto",
  "reports.organic": "orgánico",
  "reports.page_fan_adds_unique": "Seguidores",
  "reports.page_fan_adds_unique_Description":
    "La cantidad de personas nuevas que disfrutaron de su página.",
  "reports.page_impressions": "Impresiones de página",
  "reports.page_impressions_Description":
    "El número de veces cualquier contenido en su página o en ella ha aparecido en la pantalla de una persona.Esto incluye publicaciones, historias, anuncios, así como otro contenido o información en su página.",
  "reports.page_posts_impressions": "Publicaciones de la página",
  "reports.page_posts_impressions_Description":
    "La cantidad de veces que las publicaciones de su página aparecieron en la pantalla de una persona.Las publicaciones incluyen estado, fotos, enlaces, videos y más.",
  "reports.page_views_total": "Vistas de la página",
  "reports.page_views_total_Description":
    "El número de veces el perfil de una página ha sido visto por personas conectadas y desconectadas.",
  "reports.preset_date": "Período de selección",
  "reports.range": "Rango",
  "reports.rangeDescription":
    "Esta métrica representa el alcance de la distribución orgánica o pagada de su contenido de Facebook, incluidas publicaciones, historias y anuncios.También incluye el alcance de otras fuentes, como marcas, cheques y visitas a la página o perfil.Este número también incluye el alcance de publicaciones e historias que han sido turboalimentadas.El rango solo se calcula una vez si ocurre a través de la distribución orgánica y pagada.Esta métrica se estima.",
  "reports.reach": "Rango",
  "reports.recipient": "Beneficiario",
  "reports.recipients": "Receptores",
  "reports.rejected": "Rechazado",
  "reports.rejected.singular": "Rechazado",
  "reports.replied.singular": "Respondido",
  "reports.selectPage": "Página de selección",
  "reports.sent": "Adjunto",
  "reports.sent.singular": "Enviado",
  "reports.shots": "Disparos",
  "reports.softBounces": "Rebotes suaves",
  "reports.spend": "Costo",
  "reports.this_month": "Este mes",
  "reports.this_week_sun_today": "Esta semana",
  "reports.this_year": "Este año",
  "reports.today": "Hoy",
  "reports.total": "Total",
  "reports.uniqueRecipients": "Destinatarios únicos",
  "reports.unsubscribed": "Cancelaciones",
  "reports.unsubscribed.singular": "Cancelación",
  "reports.viewed": "Vista",
  "reports.viewed.singular": "Visa",
  "reports.yesterday": "Ayer",
  "roles.accountOwner": "Propietario de la cuenta",
  "roles.admin": "Administrador del sistema",
  "roles.admins": "Administradores del sistema",
  "roles.affiliate": "Filial",
  "roles.affiliates": "Afiliados",
  "roles.lead": "Dirigir",
  "roles.leads": "Dirige",
  "roles.manager": "Gerente de cuentas",
  "roles.managers": "Gerentes de cuentas",
  "roles.operator": "Operador",
  "roles.operators": "Operadores",
  "roles.owner": "Administrador",
  "roles.owners": "Administradores",
  "roles.promoter": "Promotor",
  "roles.promoters": "Fiscales",
  "roles.role": "Función",
  "roles.roles": "Deberes",
  "roles.seller": "Vendedor",
  "roles.sellers": "Vendedor",
  "roles.webmaster": "Webmaster",
  "roles.webmasters": "Webmasters",
  "roulette.equal": "Distribución equitativa",
  "roulette.random": "Distribución aleatoria",
  "segmentations.add.leads": "¿Agregar cables a la segmentación?",
  "segmentations.add.name": "Nombre de la nueva segmentación",
  "segmentations.add.new": "Crear una nueva segmentación",
  "segmentations.add.new.text":
    "Estás a punto de crear una nueva segmentación con clientes potenciales listados, ¿quieres continuar?",
  "segmentations.dinamic": "Segmentación dinámica",
  "segmentations.dinamicAlert":
    "Esta segmentación fue designada como dinámica cuando se creó.No es posible cambiar el tipo de segmentación después de su creación",
  "segmentations.dinamicConfigAlert":
    "Al guardar la segmentación por primera vez, se configurará como estática o dinámica y no será posible cambiarla más tarde.",
  "segmentations.dinamicLeads": "Proporcas en la segmentación",
  "segmentations.dinamicLeadsMatches":
    "Conduce que coinciden con los criterios de segmentación",
  "segmentations.dinamicType": "Dinámica",
  "segmentations.static": "Estático",
  "segmentations.staticAlert":
    "Esta segmentación fue designada como estática cuando se creó.No es posible cambiar el tipo de segmentación después de su creación",
  "segmentations.staticType": "Estático",
  "segmentations.title": "Segmentación",
  "sellers.roulette": "Vendedor",
  "shortcodes.data:contractNumber": "Número de contrato",
  "shortcodes.data:total": "Solicitud total",
  "shortcodes.date": "Fecha de creación",
  "shortcodes.ID": "Número",
  "shortcodes.items": "Ordenar artículos",
  "shortcodes.modified": "Fecha de modificación",
  "shortcodes.payment:gateway": "Forma de pago",
  "shortcodes.sentDate": "Día del mes de envío",
  "shortcodes.sentDay": "Día de la semana de envío",
  "shortcodes.sentFullDate": "Fecha de envío",
  "shortcodes.sentMonth": "Mes de envío",
  "shortcodes.sentPlusDate": "Fecha del envío",
  "shortcodes.sentYear": "Año de envío",
  "shortcodes.tips.sentDate": "Ej: 31",
  "shortcodes.tips.sentDay": "Ex: lunes",
  "shortcodes.tips.sentFullDate": "DD/MM/AAAA",
  "shortcodes.tips.sentMonth": "Ej: marzo",
  "shortcodes.tips.sentYear": "Ej.: 2020",
  "shortcodes.tips.type": "Individuo o empresa",
  "shortcodes.type": "Tipo de cuenta",
  "shotx.add.apiKeys": "Añade tus claves de ShotFlow aquí",
  "shotx.apiKeys.saveApiKeysSucessfully": "Llaves guardadas con éxito",
  "shotx.broadcast.cancel": "Cancelar",
  "shotx.broadcast.cancel.dialog.description":
    "¿Estás seguro de que quieres cancelar el envío del mensaje de masa?",
  "shotx.broadcast.cancel.dialog.title": "Cancelación de mensajes de masa",
  "shotx.broadcast.edit": "Edición",
  "shotx.broadcast.failed": "No entregado",
  "shotx.broadcast.indicators": "Indicadores",
  "shotx.broadcast.noBroadcasts": "No hay mensajes masivos",
  "shotx.broadcast.selectPlataform": "Seleccione la plataforma para enviar mensajes",
  "shotx.broadcast.schedulAlert": "🔔 Recuerda: Los mensajes masivos programados se pueden editar hasta 5 minutos antes del envío.",
  "shotx.broadcast.selectContent": "Elige el tipo de contenido a enviar",
  "shotx.broadcast.selectQuickMessage": "Seleccione el mensaje que se enviará a los contactos",
  "shotx.broadcast.selectQuickMessageView": "Vista previa del mensaje que se enviará a los contactos",//
  "shotx.broadcast.selectSniper": "Seleccione el Sniper que se asignará a los contactos",//
  "shotx.broadcast.noSegmentations": "No hay segmentación disponible",//
  "shotx.broadcast.allCampsOk": "¡Listo! Haz clic en el botón de abajo para terminar.",//
  "shotx.broadcast.checkInputs": "Asegúrate de haber seleccionado una instancia, contactos y un mensaje/francotirador",//
  "shotx.broadcast.requiredSchedule": "También es necesario fijar una fecha de programación.",//
  "shotx.broadcast.completAllCamps": "Complete todos los campos obligatorios para continuar",
  "shotx.broadcast.selectSegmentations": "Seleccione segmentaciones para filtrar contactos",
  "shotx.broadcast.selectLeads": "Seleccione los contactos que recibirán el mensaje",
  "shotx.broadcast.instagram.delivered":
    "No hay datos para el tipo de Instagram",
  "shotx.broadcast.instanceSelect": "Seleccione una instancia",
  "shotx.broadcast.instanceSelectLabel": "Seleccione o busque una instancia",
  "shotx.broadcast.leadsQtd": "Cantidad de contactos",
  "shotx.broadcast.leadsSelect": "Seleccionar cables",
  "shotx.broadcast.leadsSelectLabel":
    "Seleccionar o buscar clientes potenciales",
  "shotx.broadcast.messageContent": "Contenido de mensaje",
  "shotx.broadcast.messagePreview": "Anterior del mensaje",
  "shotx.broadcast.messageSelect": "Seleccione un mensaje",
  "shotx.broadcast.messageSelectLabel": "Seleccione o busque un mensaje",
  "shotx.broadcast.messageTo": "A",
  "shotx.broadcast.multipleSelection": "Selección Múltiple",
  "shotx.broadcast.noLeads":
    "No se encontraron clientes potenciales para este caso",
  "shotx.broadcast.replieds": "Respondido",
  "shotx.broadcast.rules": "Reglas de envío",
  "shotx.broadcast.schedule": "Escala",
  "shotx.broadcast.schedule.cancel": "Cancelar",
  "shotx.broadcast.scheduleDate": "Fecha y hora de envío",
  "shotx.broadcast.segmentationsSelect": "Seleccione las segmentos",
  "shotx.broadcast.segmentationsSelectLabel":
    "Seleccione o busque segmentación",
  "shotx.broadcast.segmetationsQtd": "Cantidad de segmentación",
  "shotx.broadcast.selectAll": "Seleccionar todo",
  "shotx.broadcast.selectedInstances": "Instancias Seleccionadas",
  "shotx.broadcast.selectInstance":
    "Seleccione una instancia para cargar los cables disponibles",
  "shotx.broadcast.selectInstances":
    "Seleccione una o más instancias para cargar los cables disponibles",
  "shotx.broadcast.selectNone": "Selección limpia",
  "shotx.broadcast.sendingStatus": "Estado de envío",
  "shotx.broadcast.sendLater": "Escala",
  "shotx.broadcast.sendNow": "Entregar",
  "shotx.broadcast.sendTypeSelect": "Seleccione el formulario de envío",
  "shotx.broadcast.sendTypeSniper": "Desencadenar ShotFlow",
  "shotx.broadcast.sendTypeText": "Mensaje",
  "shotx.broadcast.sent": "Enviado",
  "shotx.broadcast.sniperSelect": "Seleccione un ShotFlow",
  "shotx.broadcast.sniperSelectLabel": "Seleccione o busque un ShotFlow",
  "shotx.broadcast.status.sent": "Enviado",
  "shotx.broadcast.tagsFilter": "Etiqueta",
  "shotx.broadcast.title": "Envío de mensajes de masa",
  "shotx.broadcast.title.analytcs": "[%Type] [%date_schedule] [%plataforma]",
  "shotx.broadcast.title.editing": "Edición [%Tipo] [%date_schedule]",
  "shotx.broadcast.total": "Total",
  "shotx.broadcast.interval": "Intervalo de mensajes",
  "shotx.broadcast.intervalQty": "Cantidad",
  "shotx.broadcast.intervalUnit": "Unidad",
  "shotx.broadcast.validation.noInstancesSelected": "Seleccione al menos una instancia",
  "shotx.broadcast.validation.noInstanceSelected": "Seleccione una instancia",
  "shotx.broadcast.validation.invalidInstances": "Algunas instancias seleccionadas están inactivas o no disponibles",
  "shotx.broadcast.validation.invalidInstance": "La instancia seleccionada está inactiva o no disponible",
  "shotx.broadcast.validation.noRecipientsSelected": "Seleccione al menos un cliente potencial o una segmentación",
  "shotx.broadcast.validation.noLeadsSelected": "Seleccione al menos un cliente potencial",
  "shotx.broadcast.validation.noMessageSelected": "Seleccione un mensaje",
  "shotx.broadcast.validation.noMessagesSelected": "Seleccione al menos un mensaje",
  "shotx.broadcast.validation.invalidMessages": "Algunos mensajes seleccionados son inválidos",
  "shotx.broadcast.validation.invalidMessage": "El mensaje seleccionado es inválido",
  "shotx.broadcast.validation.noSniperSelected": "Seleccione un ShotFlow",
  "shotx.broadcast.validation.invalidSniper": "El ShotFlow seleccionado es inválido",
  "shotx.broadcast.validation.noScheduleDateSelected": "Defina una fecha de programación",
  "shotx.broadcast.warning.inactiveInstances": "Instancias inactivas detectadas",
  "shotx.broadcast.warning.noValidLeads": "No se encontraron clientes potenciales válidos en las instancias seleccionadas",
  "shotx.broadcast.error.loadingLeads": "Error al cargar clientes potenciales de las instancias",
  "shotx.broadcast.instanceOrder": "Orden de las Instancias",
  "shotx.broadcast.instanceSequential": "Secuencial",
  "shotx.broadcast.instanceRandom": "Aleatoria",
  "shotx.broadcast.messageMultipleSelection": "Selección Múltiple de Mensajes",
  "shotx.broadcast.selectedMessages": "Mensajes Seleccionados",
  "shotx.broadcast.messageOrder": "Orden de los Mensajes",
  "shotx.broadcast.messageSequential": "Secuencial",
  "shotx.broadcast.messageRandom": "Aleatoria",
  "shotx.chat.audioNotAvailable": "Audio no disponible",
  "shotx.chat.discard": "Desechar",
  "shotx.chat.fileNotAvailable": "Archivo no disponible",
  "shotx.chat.imageNotAvailable": "Imagen no disponible",
  "shotx.chat.send": "Para enviar",
  "shotx.chat.sendMedia": "Medios de comunicación",
  "shotx.chat.videoNotAvailable": "Video no disponible",
  "shotx.connectedNumber": "Número conectado",
  "shotx.instagram.instance.create.info":
    "No es posible conectar la misma cuenta de Instagram en más de una instancia",
  "shotx.instagram.instance.title.placeholder":
    "Nombre de instancia de Instagram",
  "shotx.instagram.noContact":
    "Enlace una interacción al plomo para enviar un mensaje.",
  "shotx.instagram.noIntegrations": "No hay integración configurada",
  "shotx.instagram.notReady": "Instagram aún no está listo para su uso",
  "shotx.instagram.onRefused": "Conexión rechazada",
  "shotx.instagram.selectLead": "Seleccione un plomo",
  "shotx.instance.connect": "Conectar",
  "shotx.instance.connected": "Conectado",
  "shotx.instance.connectedSuccessfully": "Conectado con éxito",
  "shotx.instance.connecting": "Conexión",
  "shotx.instance.create": "Crear instancia",
  "shotx.instance.disconnect": "Desconectar",
  "shotx.instance.disconnected": "Desconectado",
  "shotx.instance.disconnectedInfo":
    "Desconectado.Conecte Qiplus a su [Plataforma%] e intente nuevamente.",
  "shotx.instance.disconnectedSuccessfully": "Desconectado con éxito",
  "shotx.instance.disconnecting": "Desconectar",
  "shotx.instance.edit": "Edición [%instancia]",
  "shotx.instance.interactions": "Interacciones",
  "shotx.instance.interactions.notVinculated": "Sin plomo vinculado",
  "shotx.instance.interactions.title": "Interacciones de [%instancia]",
  "shotx.instance.interactions.unvinculate": "Desconectar",
  "shotx.instance.interactions.vinculate": "Dirigir",
  "shotx.instance.loading": "Cargando",
  "shotx.instance.noMessages": "Sin mensaje",
  "shotx.instance.qrcode": "Lea el Ccode QR con su aplicación WhatsApp.",
  "shotx.instance.refused":
    "El tiempo para la conexión expiró, intente nuevamente",
  "shotx.instance.saveChangesSuccessfully": "Cambios guardados exitosos",
  "shotx.instance.willBeDeleted":
    "Al eliminar la instancia, todos los datos y mensajes se moverán a la basura.¿Quieres continuar?Esta operación no se puede deshacer",
  "shotx.module.title": "Shotx",
  "shotx.module.tooltip": "Chats integrados con shotx",
  "shotx.plataform.config": "Ajustes",
  "shotx.plataform.title": "Plataformas",
  "shotx.plataform.whatsapp.title": "Whatsapp",
  "shotx.snipers.apiKeyToken": "Token API",
  "shotx.snipers.form.apiKey": "Agregue su token API",
  "shotx.snipers.form.keyword": "Palabra clave",
  "shotx.snipers.form.keywordFinish": "Palabra para terminar Shotflow",
  "shotx.snipers.form.keywordFinishPlaceholder":
    "Palabra para terminar la conversación con Shotflow",
  "shotx.snipers.form.selectTrigger": "Desencadenar",
  "shotx.snipers.form.selectTypeTrigger": "Seleccionar el tipo de activación",
  "shotx.snipers.form.trigger": "Gatito",
  "shotx.snipers.form.typeTrigger": "Desencadenar",
  "shotx.snipers.form.unknownMessage": "Mensaje desconocido",
  "shotx.snipers.form.unknownMessagePlaceholder":
    "Frase al recibir una opción desconocida",
  "shotx.snipers.form.withoutApiKey":
    "Token de API requerido para actualizar los datos de los principales",
  "shotx.snipers.form.withoutTitle":
    "Informe a la identificación pública del Shotflow",
  "shotx.snipers.noApiKeyToken":
    "No se encuentra la clave, registre sus claves en la pestaña Configuración",
  "shotx.snipers.nomeSniper": "Nombre del Shotflow (bot)",
  "shotx.snipers.noSniper":
    "¡No hay Shotflow creado en este espacio de trabajo!",
  "shotx.snipers.operator.contains": "Contener",
  "shotx.snipers.operator.endsWith": "Termina con",
  "shotx.snipers.operator.equals": "Igual",
  "shotx.snipers.operator.startsWith": "Comienza con",
  "shotx.snipers.scheduledMessage": "Servicio programado exitoso",
  "shotx.snipers.title": "Shotflows",
  "shotx.snipers.typeOperador": "Tipo / operador",
  "shotx.snipers.types.all": "Todo",
  "shotx.snipers.types.keyword": "Palabra clave",
  "shotx.snipers.value": "Valor",
  "shotx.statuses.close": "Desconectado",
  "shotx.statuses.connecting": "Conexión",
  "shotx.statuses.open": "Conectado",
  "shotx.statuses.refused": "Rechazado",
  "shotx.tabs.config": "Ajustes",
  "shotx.tabs.ImportSniper": "Importar Shotflow",
  "shotx.tabs.instance": "Instancia",
  "shotx.tabs.sniper": "Shotflow",
  "shotx.whatsapp.assignedToSniper": "Servicio asignado a Shotflow!",
  "shotx.whatsapp.assigningToSniper": "Atribución de servicio al Shotflow",
  "shotx.whatsapp.assignToSniper": "Asignar servicio de Shotflow",
  "shotx.whatsapp.associatedFieldSniper": "Shotflow varios",
  "shotx.whatsapp.associatedLeadFieldSniper":
    "Campo para actualizar en el plomo",
  "shotx.whatsapp.clickToEnd": "Fin",
  "shotx.whatsapp.clickToStart": "Comenzar",
  "shotx.whatsapp.ended": "Servicio cerrado por @username en @Date.",
  "shotx.whatsapp.instance.alert":
    "Cambiar el nombre de la instancia causará la pérdida del historial de mensajes.",
  "shotx.whatsapp.instance.body.title": "Instancia",
  "shotx.whatsapp.instance.info": "Ingrese una descripción para la instancia.",
  "shotx.whatsapp.instance.title.info": "Ingrese el nombre de la instancia",
  "shotx.whatsapp.instance.title.placeholder":
    "Nombre de instancia de WhatsApp",
  "shotx.whatsapp.iWillAnswer": "Me reuniré",
  "shotx.whatsapp.noContact":
    "Enlace un contacto de teléfono celular válido para enviar un mensaje.",
  "shotx.whatsapp.noIntegrations": "No hay integración configurada",
  "shotx.whatsapp.noQuickMessages": "No hay un mensaje rápido registrado.",
  "shotx.whatsapp.noSniper": "No hay Shotflow",
  "shotx.whatsapp.notReady": "WhatsApp aún no está listo para su uso",
  "shotx.whatsapp.notStarted":
    "Todavía no ha iniciado el servicio, haga clic en Inicio para responder.",
  "shotx.whatsapp.qrcode.button.disconnect": "Desconectar",
  "shotx.whatsapp.qrcode.button.refresh": "Actualizar QRCode",
  "shotx.whatsapp.qrcode.info": "Lea el Ccode QR con su aplicación WhatsApp.",
  "shotx.whatsapp.qrcode.loading": "Generación de QRCode ...",
  "shotx.whatsapp.request.createinstance.fail": "Falla al crear la instancia",
  "shotx.whatsapp.selectSniper": "Seleccione un Shotflow",
  "shotx.whatsapp.started": "Servicio iniciado por @username en @Date.",
  "shotx.whatsapp.status.connected": "Tienes un número conectado a WhatsApp.",
  "shotx.whatsapp.status.disconnected": "Desconectado",
  "sidebar.": "",
  "sidebar.404": "404",
  "sidebar.500": "500",
  "sidebar.aboutUs": "Sobre nosotros",
  "sidebar.account": "Mi cuenta",
  "sidebar.add": "Para agregar",
  "sidebar.addNew": "Para crear",
  "sidebar.agency": "Agencia",
  "sidebar.alerts": "Advertencia",
  "sidebar.analytics": "Analítica",
  "sidebar.app": "Aplicación",
  "sidebar.applications": "Aplicaciones",
  "sidebar.blank": "Blanco",
  "sidebar.boxed": "En caja",
  "sidebar.calendar": "Calendario",
  "sidebar.cart": "Carro",
  "sidebar.chat": "Charlar",
  "sidebar.checkout": "Verificar",
  "sidebar.clients": "clientes",
  "sidebar.component": "Componentes",
  "sidebar.contact": "Contacto",
  "sidebar.crm": "CRM",
  "sidebar.crypto": "Cripto",
  "sidebar.dashboard": "Panel",
  "sidebar.dateTimePicker": "Seleccionador de fecha y hora",
  "sidebar.dropzone": "Zonas",
  "sidebar.ecommerce": "Comercio electrónico",
  "sidebar.editor": "Editor",
  "sidebar.email": "Correo electrónico",
  "sidebar.emails": "Correos electrónicos",
  "sidebar.event": "Evento",
  "sidebar.events": "Eventos",
  "sidebar.extensions": "Extensiones",
  "sidebar.faq(s)": "Preguntas frecuentes",
  "sidebar.features": "Fondos",
  "sidebar.feedback": "Comentario",
  "sidebar.forgotPassword": "Olvidé la contraseña",
  "sidebar.forms": "Formularios",
  "sidebar.funnel": "Embudo de ventas",
  "sidebar.funnels": "Embudos de ventas",
  "sidebar.gallery": "Galería",
  "sidebar.general": "General",
  "sidebar.gettingStarted": "Empezando",
  "sidebar.home": "Hogar",
  "sidebar.horizontal": "Horizontal",
  "sidebar.horizontalMenu": "Menú horizontal",
  "sidebar.icons": "Íconos",
  "sidebar.imageCropper": "Herramienta de recorte",
  "sidebar.inbox": "Bandeja de entrada",
  "sidebar.invoice": "Factura",
  "sidebar.leads": "Dirige",
  "sidebar.level1": "Nivel 1",
  "sidebar.level2": "Nivel 2",
  "sidebar.listAction": "Prodigar",
  "sidebar.lockScreen": "Pantalla de bloqueo",
  "sidebar.login": "Acceso",
  "sidebar.mailbox": "Bandeja de entrada",
  "sidebar.mailboxes": "Bandeja de entrada",
  "sidebar.mailing": "Envío",
  "sidebar.miscellaneous": "Variado",
  "sidebar.model": "Modelo",
  "sidebar.models": "Modelos",
  "sidebar.module": "Módulo",
  "sidebar.modules": "Módulos",
  "sidebar.multilevel": "Multinivel",
  "sidebar.multiLevel": "Multinivel",
  "sidebar.news": "Noticias",
  "sidebar.pages": "Páginas",
  "sidebar.pipeline": "Tubería",
  "sidebar.pipelines": "Tuberías",
  "sidebar.plans.dashboard": "Informe financiero",
  "sidebar.pricing": "Precios",
  "sidebar.product": "Producto",
  "sidebar.products": "Productos",
  "sidebar.progress": "Progreso",
  "sidebar.projectDetail": "Detalles del proyecto",
  "sidebar.projects": "proyectos",
  "sidebar.promo": "Promoción",
  "sidebar.register": "Registro",
  "sidebar.report": "Informe",
  "sidebar.reports": "Informes",
  "sidebar.reports.automations.ads": "Ads de Google",
  "sidebar.saas": "SaaS",
  "sidebar.segmentationsGroup": "Segmentación",
  "sidebar.session": "Sesión",
  "sidebar.shop": "Comercio",
  "sidebar.shopGrid": "Tienda de redes",
  "sidebar.shopList": "Lista de tiendas",
  "sidebar.sublevel": "Subyacente",
  "sidebar.tables": "Mesa",
  "sidebar.terms&Conditions": "Términos y condiciones",
  "sidebar.toDo": "Todo",
  "sidebar.toggle": "Mostrar/ocultar barra lateral",
  "sidebar.tools": "Herramientas",
  "sidebar.user": "Usuario",
  "sidebar.userList": "Lista de usuarios",
  "sidebar.userManagement": "Gestión de usuarios",
  "sidebar.userProfile": "Perfil de usuario",
  "sidebar.users": "Usuarios",
  "sidebar.videoPlayer": "Reproductor de video",
  "sniper.botAvailable": "Opción disponible después de conectar su instancia",
  "sniper.botDisabled": "Bot discapacitado",
  "sniper.botEnabled": "Bot calificado",
  "sniper.botId": "Shotflow Public Id",
  "sniper.module.title": "Shotflow",
  "sniper.module.tooltip": "Automatización de conversación",
  "sounds.off": "Desactivar sonidos",
  "sounds.on": "Sonido",
  "stats.adsStats": "Métricas publicitarias",
  "stats.automationsConvertions": "Conversaciones de automatización",
  "stats.automationsFired": "Tomas de automatización",
  "stats.average": "Promedio",
  "stats.CAC": "Costo de adquisición de clientes",
  "stats.campaignsFired": "Tiros de campaña",
  "stats.campaignStats": "Métricas de campaña",
  "stats.cap": "Captura",
  "stats.conversionAvg": "Valor de conversión promedio",
  "stats.conversionCost": "Costo de conversión",
  "stats.conversionCycle": "Ciclo de ventas",
  "stats.conversionCycleAvgTime": "Tiempo promedio del ciclo de ventas",
  "stats.conversionRate": "Tasa de conversión",
  "stats.conversions": "Conversiones",
  "stats.conversionsChart": "Estadística comercial",
  "stats.conversionsCount": "Órdenes realizadas",
  "stats.dealInsWonStage": "Negocio marcado como ganancias",
  "stats.dealsAdded": "Nuevo negocio",
  "stats.dealsAddedValue": "Valor de nuevos negocios",
  "stats.dealsLost": "Negocio perdido",
  "stats.dealsLostRate": "Tasa comercial perdida",
  "stats.dealsLostValue": "Valor comercial perdido",
  "stats.dealsStats": "Métricas comerciales",
  "stats.dealsWon": "Ganancias comerciales",
  "stats.dealsWonRate": "Tasa comercial cerrada",
  "stats.dealsWonValue": "Ganancias de valor comercial",
  "stats.emailsClickRate": "Tarifa de clics",
  "stats.emailsOpenCount": "Volumen de apertura",
  "stats.emailsOpenRate": "Tasa de apertura",
  "stats.emailsSentCount": "Volumen de",
  "stats.emptyMsg": "No hay datos para el período por ahora",
  "stats.formsSubmitions": "Formas llenas",
  "stats.funnelsConvertions": "Conversaciones en embudo",
  "stats.generated": "Generado",
  "stats.geoLocation": "Geolocalización",
  "stats.investmentsTotal": "Inversión total",
  "stats.landingSessions": "Sesiones de páginas de destino",
  "stats.landingStats": "Métricas de páginas de destino",
  "stats.leadsBaseSize": "Tamaño base",
  "stats.leadsCap": "Conducir",
  "stats.leadsConverted": "Clientes con conversiones",
  "stats.leadsDailyCap": "Captura diaria",
  "stats.leadsReached": "Cables afectados",
  "stats.leadsRegistered": "Absorción completa",
  "stats.lost": "Perdido",
  "stats.mailingStats": "Métricas de envío",
  "stats.net": "Líquido",
  "stats.netRevenue": "Ingresos netos",
  "stats.new": "Nuevo",
  "stats.opportunities": "Oportunidades",
  "stats.QtdDeals": "Cantidad de negocios",
  "stats.ROI": "Retorno de la inversión",
  "stats.sessions": "Sesiones",
  "stats.sessionsCount": "Visitas totales",
  "stats.sessionsIn": "Sesiones en",
  "stats.teamsRanking": "Clasificación de equipo",
  "stats.teamsStats": "Métricas de equipo",
  "stats.ticketsAddedCount": "Órdenes realizadas",
  "stats.ticketsAverage": "Boleto promedio",
  "stats.ticketsPerDeal": "Solicitudes de negocios",
  "stats.ticketsTotal": "Ingresos acumulados",
  "stats.tooltip.CAC":
    "Los costos de adquisición del cliente (o el costo de adquisición de clientes) define el monto gastado por la compañía para convertir un liderazgo en un comprador o un usuario de los servicios ofrecidos.Fórmula de cálculo: Costo total para la adquisición del cliente / Total de nuevos clientes = CAC",
  "stats.tooltip.LTV":
    "Lifetime Value (LTV) identifica el valor que cada cliente ha dejado en su empresa durante el período que ha consumido sus productos y servicios.Fórmula de cálculo: (valor promedio de una venta) x (tiempo promedio de retención en meses o años para un cliente típico) = LTV",
  "stats.tooltip.ROI":
    "El retorno de la inversión es un indicador que permite a las empresas saber cuánto ha ganado o perdido con sus inversiones.Fórmula de cálculo: (retorno de la inversión - costo de inversión) / costo de inversión = ROI",
  "stats.total": "Total",
  "stats.traffic": "Tráfico",
  "stats.trafficSources": "Orígenes del tráfico",
  "stats.uniqueLeads": "Pistas únicas",
  "stats.uniqueUsers": "Usuarios únicos",
  "stats.uniqueVisitors": "Visitantes únicos",
  "stats.valueAverage": "Valor promedio",
  "stats.visitors": "Visitantes",
  "stats.won": "Ganancias",
  "stats.wonCycle": "Ciclo comercial",
  "stats.wonCycleAvgTime": "Tiempo promedio del ciclo económico",
  "stats.wonCycleClosingTime": "Tiempo de conclusión promedio",
  "statuses.active": "Activo",
  "statuses.active.fem": "Activo",
  "statuses.draft": "Bosquejo",
  "statuses.draft.fem": "Bosquejo",
  "statuses.inactive": "Inactivo",
  "statuses.inactive.fem": "Inactivo",
  "statuses.model": "Modelo",
  "statuses.publish": "Publicado",
  "statuses.publish.fem": "Publicado",
  "statuses.scheduled": "Programado",
  "statuses.scheduled.fem": "Programado",
  "statuses.send": "Enviado",
  "statuses.send.fem": "Adjunto",
  "statuses.sending": "Envío",
  "statuses.sending.fem": "Envío",
  "statuses.sent": "Enviado",
  "statuses.sent.fem": "Adjunto",
  "statuses.trash": "Papelera",
  "statuses.trash.fem": "Papelera",
  "stock.available": "Disponible",
  "stock.initial": "Para la campaña",
  "stock.label": "Existencias",
  "stock.real": "Stock físico",
  "stock.used": "Usado",
  "styles.backgroundColor": "Fondo",
  "styles.borderColor": "Color de borde",
  "styles.borderWidth": "Espesor del borde",
  "styles.buttonBackground": "Color de fondo del botón",
  "styles.buttonColor": "Color de texto del botón",
  "styles.buttonOptionsLarge": "Grande",
  "styles.buttonOptionsLeft": "Izquierda",
  "styles.buttonOptionsMedium": "Promedio",
  "styles.buttonOptionsMiddle": "Centro",
  "styles.buttonOptionsRight": "Bien",
  "styles.buttonOptionsSmall": "Pequeño",
  "styles.buttonPosition": "Posición del botón",
  "styles.buttonText": "Texto de botón",
  "styles.customCSS": "CSS personalizado",
  "styles.fieldBackground": "Fondo de campo",
  "styles.fieldBorderColor": "Color de borde de campo",
  "styles.fieldColor": "Color de campo",
  "styles.labelColor": "Etiqueta",
  "styles.sizeButton": "Tamaño del botón",
  "subscription.billing": "Ganancia",
  "subscription.boleto": "Bancos",
  "subscription.canceled": "Cancelado",
  "subscription.credit_card": "Tarjeta de crédito",
  "subscription.ended": "Finalizado",
  "subscription.overview.amount": "Valor",
  "subscription.overview.bankSlips": "Resbalones bancarios",
  "subscription.overview.canceled": "Cancelado",
  "subscription.overview.canceleds": "Cancelado",
  "subscription.overview.count": "Cantidad",
  "subscription.overview.creditCards": "Tarjetas de crédito",
  "subscription.overview.customer": "Cliente",
  "subscription.overview.dailyRevenues": "Ingresos diarios",
  "subscription.overview.geographicDistribution": "Distribución geográfica",
  "subscription.overview.list": "Firmas",
  "subscription.overview.monthlyRevenues": "Ingresos mensuales",
  "subscription.overview.paid": "Pagado",
  "subscription.overview.paids": "Pagado",
  "subscription.overview.paymentMethod": "Forma de pago",
  "subscription.overview.period": "Período",
  "subscription.overview.plan": "Departamento",
  "subscription.overview.plansTotals": "Totales por plan",
  "subscription.overview.state": "Estado",
  "subscription.overview.status": "Estado",
  "subscription.overview.statusDistribution": "Distribución de estado",
  "subscription.overview.subscriptionsEvolution": "Evolución de las firmas",
  "subscription.overview.total": "Firmas activas",
  "subscription.overview.totalRevenues": "Ingresos totales",
  "subscription.overview.unpaid": "Pendiente",
  "subscription.overview.unpaids": "Colgantes",
  "subscription.paid": "Pagado",
  "subscription.pending_payment": "Pendiente",
  "subscription.status": "Estado de firma",
  "subscription.trialing": "Ensayo",
  "subscription.unpaid": "No pagado",
  "tables.loadingText": "Cargando...",
  "tables.nextText": "Próximo",
  "tables.noDataText": "No hay elementos para mostrar",
  "tables.ofText": "de",
  "tables.pageText": "Página",
  "tables.previousText": "Anterior",
  "tables.rowsText": "",
  "tables.summaryText": "Página {desde} de {país}",
  "tags.dealTags": "Etiquetas de negocios",
  "tags.leadTags": "Etiquetas de plomo",
  "task.add": "Nueva tarea",
  "task.clone": "Tarea clon",
  "task.create": "Crear tarea",
  "task.edit": "Editar tarea",
  "tasklists.label.importance": "Peso",
  "tasklists.label.title": "Título",
  "tasklists.task_question.import": "Importar",
  "tasks.clone": "Tareas clon",
  "tasks.edit": "Editar tareas",
  "texts.actions": "Comportamiento",
  "texts.AddItemsToStartActions": "Arrastre las acciones aquí",
  "texts.AddItemsToStartTriggers": "Arrastre los desencadenantes aquí",
  "texts.addNewPost": "Crear [%s]",
  "texts.addVideoPoster": "Agregar póster al video",
  "texts.affiliateHomeLink": "Enlace del hogar afiliado",
  "texts.affiliateLink": "Enlace de afiliado",
  "texts.allActions": "Todas las acciones",
  "texts.alreadyHaveAccount": "¿Ya tienes una cuenta?",
  "texts.alreadyHavingAccountSignIn": "¿Ya tienes una cuenta?Acceso",
  "texts.and": "y",
  "texts.attachments": "Accesorios",
  "texts.banner": "Bandera",
  "texts.banners": "Pancartas",
  "texts.bannersKit": "Banner",
  "texts.campaign": "Campaña",
  "texts.cannotBeUndone": "Esta acción no se puede deshacer",
  "texts.changeMyPlan": "Cambiar mi plan",
  "texts.check-in": "registrarse",
  "texts.checkin": "Registrarse",
  "texts.checkin.plural": "Registrarse",
  "texts.choices": "Opción",
  "texts.chooseAnotherPlan": "Elija otro plan",
  "texts.chooseYourPlan": "Elige tu plan",
  "texts.clickHereToSetup": "Haga clic aquí para configurar",
  "texts.clickHereToStart": "Haga clic aquí para comenzar",
  "texts.column": "Columna",
  "texts.columns": "Columnas",
  "texts.commaSeparatedEmails":
    "Direcciones de correo electrónico separadas por coma",
  "texts.commaSeparatedTags": "Etiquetas separadas por coma",
  "texts.comments": "Comentario",
  "texts.completed": "Lleno",
  "texts.confirmAllFieldsRemoval": "¿Quieres eliminar todos los campos?",
  "texts.confirmed": "Confirmado",
  "texts.confirmed.plural": "Confirmado",
  "texts.confirmFieldRemoval": "¿Quieres eliminar este campo?",
  "texts.content": "Contenido",
  "texts.contentType": "Tipo de contenido",
  "texts.conversion": "Conversión",
  "texts.conversionGoals": "Objetivos de conversión",
  "texts.conversions": "Conversiones",
  "texts.copiedToClipboard": "Copiado en el área de transferencia",
  "texts.copy": "Copiar",
  "texts.copyAffiliateHomeLink": "Copiar enlace de inicio del afiliado",
  "texts.copyAffiliateLink": "Copiar enlace de afiliado",
  "texts.copyEmbedCode": "Copiar código HTML para insertar",
  "texts.copyImageUrl": "Copiar la URL de la imagen",
  "texts.copyParentLink": "Copiar enlace de matriz",
  "texts.copyToClipboard": "Copiar",
  "texts.copyVideoUrl": "Copiar la URL de video",
  "texts.count": "Contar",
  "texts.customBanners": "Pancartas personalizadas",
  "texts.customer": "Cliente",
  "texts.customers": "Clientes",
  "texts.customizeYourPlan": "Personaliza tu plan",
  "texts.customVideos": "Videos personalizados",
  "texts.data": "Datos",
  "texts.date": "Fecha",
  "texts.dateRegistered": "Fecha de registro",
  "texts.dates": "Fechas",
  "texts.deadline": "Fecha límite",
  "texts.dealTitle": "Título de negocios",
  "texts.description": "Descripción",
  "texts.discardImage": "Deseche la imagen",
  "texts.discardVideo": "Deseche el video",
  "texts.document": "Documento",
  "texts.documents": "Documento",
  "texts.dontHaveAccount": "¿No tienes una cuenta todavía?",
  "texts.dropItemsHere": "Arrastre y suelte elementos aquí",
  "texts.email": "Correo electrónico",
  "texts.emails": "Correos electrónicos",
  "texts.embed": "Avergonzar",
  "texts.embedCreated": "¡Incruta creada con éxito!",
  "texts.enable": "Permitir",
  "texts.end": "Fin",
  "texts.enterPasswords": "Contraseñas",
  "texts.enterYourEmail": "Tu correo electrónico",
  "texts.enterYourName": "Su nombre",
  "texts.enterYourPassword": "Ingrese su contraseña",
  "texts.equal_mode": "Equitativo",
  "texts.fetchingItems": "Cargando...",
  "texts.fillActionData": "Completar los campos de la acción",
  "texts.filterAction": "Filtrar",
  "texts.filterBy": "Filtrar",
  "texts.filterChoices": "Opciones de filtro",
  "texts.filterValues": "Seleccionado",
  "texts.forgotPassword": "Olvidé la contraseña",
  "texts.goal": "Meta",
  "texts.goalName": "Nombre de gol",
  "texts.goals": "Objetivos",
  "texts.goalTarget": "Aumentar",
  "texts.goalType": "Tipo de objetivo",
  "texts.graphicPieces": "Piezas gráficas",
  "texts.hi": "Hola",
  "texts.historial": "Histórico",
  "texts.if": "Si",
  "texts.image": "Imagen",
  "texts.images": "Imágenes",
  "texts.importBannersKit": "Importar el kit de banners predeterminados",
  "texts.importSuccess": "Importado con éxito",
  "texts.in": "en",
  "texts.informYourDomain": "Ingrese su dominio",
  "texts.insertTitle": "Ingrese el título",
  "texts.instructions": "Instrucciones",
  "texts.investmentName": "Nombre de inversión",
  "texts.investments": "Inversión",
  "texts.investmentsOnPeriod": "Inversiones en el período",
  "texts.lead": "Dirigir",
  "texts.leads": "Dirige",
  "texts.leadsGoals": "Dirigir objetivos",
  "texts.leaveBlank": "Déjalo en blanco si no aplica",
  "texts.listAction": "Prodigar",
  "texts.listAll": "Lanzar todo",
  "texts.loading": "Cargando...",
  "texts.manager": "Gerente de cuentas",
  "texts.managers": "Gerentes de cuentas",
  "texts.mktBanner": "Banner promocional",
  "texts.mktBanners": "Pancartas promocionales",
  "texts.mktMaterial": "Material promocional",
  "texts.mktVideo": "Video promocional",
  "texts.mktVideos": "Videos promocionales",
  "texts.montlyPlan": "Mensual",
  "texts.myPlan": "Mi plan",
  "texts.new": "Nuevo",
  "texts.newDeal": "Nuevo negocio",
  "texts.newPost": "Nuevo artículo",
  "texts.newRegisters": "Nuevo",
  "texts.newUser": "Nuevo usuario",
  "texts.noItemsAvailable": "No hay artículo disponible",
  "texts.noItemsSelected": "No hay artículo seleccionado",
  "texts.none": "Ninguno",
  "texts.none.f": "Ninguno",
  "texts.noOperationsToPerform": "No hay operación para realizar",
  "texts.option": "Opción",
  "texts.optional": "Opcional",
  "texts.options": "Opción",
  "texts.or": "o",
  "texts.orSigninWith": "o iniciar sesión con",
  "texts.other": "Otro",
  "texts.otherActions": "Otras acciones",
  "texts.others": "Otros",
  "texts.parentLink": "Enlace comercial de árboles",
  "texts.parentTitle": "Negocio de árboles",
  "texts.participant": "Partícipe",
  "texts.participants": "Participantes",
  "texts.passwordResetEmailSent": "Correo electrónico enviado correctamente",
  "texts.payment": "Pago",
  "texts.payments": "Pagos",
  "texts.platform": "Plataforma",
  "texts.platforms": "Plataformas",
  "texts.preferences": "Preferencias",
  "texts.privateToken": "Token privado",
  "texts.processing": "Proceso",
  "texts.promoter": "Promotor",
  "texts.promoters": "Fiscales",
  "texts.qiplusUrlType": "Tipo de página",
  "texts.qiusers": "Usuarios",
  "texts.random_mode": "Aleatorio",
  "texts.redirect": "Redirección",
  "texts.redirectAction": "Redireccionar",
  "texts.redirectType": "Tipo de redirección",
  "texts.refreshToken": "Actualización",
  "texts.relate": "Relatar",
  "texts.related": "Relacionado",
  "texts.reloadSection": "Recargar",
  "texts.removeImage": "Eliminar",
  "texts.removeImageSize": "Elimine este tamaño de imagen",
  "texts.removeVideo": "Eliminar el video",
  "texts.removeVideoSize": "Elimine este tamaño de video",
  "texts.resetPassword": "Contraseña de redefinición",
  "texts.result": "Resultado",
  "texts.results": "Resultados",
  "texts.script": "Guion",
  "texts.scripts": "Guiones",
  "texts.select": "Seleccionar",
  "texts.select.intance": "Seleccione su instancia",
  "texts.select.quickMessage": "Seleccione un mensaje rápido",
  "texts.selectAnAction": "Seleccione una acción",
  "texts.selectAnItem": "Seleccione un artículo",
  "texts.selectAStage": "Seleccione un paso",
  "texts.selectATrigger": "Seleccione un disparador",
  "texts.selectDocument": "Seleccione un documento",
  "texts.selected": "Seleccionado",
  "texts.selectedPlural": "Seleccionado",
  "texts.selectImage": "Seleccionar imagen",
  "texts.selectOwner": "Seleccione una cuenta",
  "texts.seller": "Vendedor",
  "texts.sellers": "Vendedor",
  "texts.shortlink": "Recinto",
  "texts.shortlinkCreated": "¡Línea corta creada con éxito!",
  "texts.source": "Fuente",
  "texts.sources": "Fuentes",
  "texts.stage": "Paso",
  "texts.stages": "Etapas",
  "texts.start": "Comenzar",
  "texts.status": "Estado",
  "texts.store": "Almacenar",
  "texts.stores": "Víveres",
  "texts.submitedScripts": "Scripts en la página de confirmación",
  "texts.subscribed": "Inscrito",
  "texts.subscribed.plural": "Suscriptores",
  "texts.tags": "Etiquetas",
  "texts.tasklistLost": "Perdido",
  "texts.tasklistWon": "Ganancias",
  "texts.termsAndConditions": "Términos y condiciones",
  "texts.ticketItems": "Ordenar artículos",
  "texts.token": "Simbólico",
  "texts.tracking": "Seguimiento",
  "texts.treeBusinessTitle": "Negocio de árboles",
  "texts.trialDays": "Días de prueba",
  "texts.trigger": "Disparo",
  "texts.triggers": "Disparo",
  "texts.tutorial": "Tutorial",
  "texts.type": "Tipo",
  "texts.unit": "Unidad",
  "texts.units": "Unidades",
  "texts.updated": "Actualizado",
  "texts.updated.plural": "Actualizado",
  "texts.updateDeal": "Editar negocios",
  "texts.updatedRegisters": "Actualizado",
  "texts.updateUser": "Editar usuario",
  "texts.updateYourPlan": "Actualiza tu plan",
  "texts.userGoodbye": "¡Hasta la próxima!",
  "texts.userLogoutSuccess": "¡Hasta la próxima!",
  "texts.usersIncluded": "Los usuarios incluidos",
  "texts.userWelcome": "Bienvenido",
  "texts.value": "Valor",
  "texts.video": "Video",
  "texts.videoPoster": "Póster",
  "texts.wantThisPlan": "Quiero este plan",
  "texts.yearlyPlan": "Plan anual",
  "themeOptions.appSettings": "Configuración de la aplicación",
  "themeOptions.boxLayout": "Diseño de caja",
  "themeOptions.darkMode": "Dark Vision",
  "themeOptions.gridLayout": "Diseño: cuadrícula",
  "themeOptions.listLayout": "Diseño: lista",
  "themeOptions.miniSidebar": "Menú compacto",
  "themeOptions.rtlLayout": "Diseño de rtl",
  "themeOptions.sidebarBackgroundImages":
    "Imágenes de fondo de la barra lateral",
  "themeOptions.sidebarDark": "Oscuro",
  "themeOptions.sidebarImage": "Imagen de la barra lateral",
  "themeOptions.sidebarLight": "Leve",
  "themeOptions.sidebarOverlay": "Superposición de la barra lateral",
  "themeOptions.themeColor": "Color del tema",
  "time.after": "Después",
  "time.afterStart": "Después del comienzo",
  "time.before": "Antes",
  "time.beforeStart": "Antes de comenzar",
  "time.days": "días",
  "time.deadline": "Fecha límite",
  "time.hours": "horas",
  "time.invalidDateMessage": "Fecha no válida",
  "time.maxDateMessage": "La fecha es más alta al máximo permitido",
  "time.minDateMessage": "La fecha es menor que el mínimo permitido",
  "time.minutes": "minutos",
  "time.onadd": "Después de ser agregado",
  "time.onadd.fem": "Después de ser agregado",
  "time.oncreate": "Después de la creación",
  "time.oncreate.fem": "Después de la creación",
  "time.time": "Tiempo",
  "tips.saveOrUseOnlyInThisForm":
    "Puede usar este campo solo en este formulario o guardarlo en la base de datos",
  "triggers.added": "Nuevo",
  "triggers.added_as_participant": "Participante agregado",
  "triggers.added_to_funnel": "Entrada al embudo",
  "triggers.added_to_segmentation": "Agregado a la segmentación",
  "triggers.added_to_store": "Agregado a la tienda",
  "triggers.added_via_integration": "Agregado a través de la integración",
  "triggers.addedAsParticipant": "Fue agregado como participante",
  "triggers.addedToFunnel": "Entrada al embudo",
  "triggers.addedToSegmentation": "Se agregó a la segmentación",
  "triggers.addedToStore": "Se agregó a la tienda",
  "triggers.addedViaIntegration": "Se agregó a través de la integración",
  "triggers.addFromShotX": "Plomo agregado a través de shotx",
  "triggers.ageRange": "Por rango de edad",
  "triggers.answeredAQuestionnaire": "Respondió el cuestionario",
  "triggers.birthdayMonth": "Por mes de cumpleaños",
  "triggers.bought": "Comprar",
  "triggers.boughtAProduct": "Compró el producto",
  "triggers.canceled": "Cancelado",
  "triggers.cep": "Por código postal",
  "triggers.checkedIn": "Registrarse",
  "triggers.city": "Por ciudad",
  "triggers.clickedAnEmail": "Haga clic en el correo electrónico",
  "triggers.completed": "Lleno",
  "triggers.confirmed_participant": "Participación confirmada",
  "triggers.confirmedParticipation": "Participación confirmada",
  "triggers.converted": "Conversión",
  "triggers.convertedInFunnel": "Convertido al embudo",
  "triggers.country": "Por padres",
  "triggers.currentStageInFunnel": "Está en el escenario",
  "triggers.deals_added": "Nuevo negocio",
  "triggers.didNotClickAnEmail": "No se hizo clic en el correo electrónico",
  "triggers.didNotOpenEmail": "No abrió el correo electrónico",
  "triggers.didntCheckin": "Sin check-in de FEX",
  "triggers.filledAForm": "Llenó el formulario",
  "triggers.fired": "Disparo",
  "triggers.gender": "Por género",
  "triggers.impression": "Imprimir",
  "triggers.interaction_added": "Interacción vinculada a una ventaja",
  "triggers.leadGenerated": "Plomo capturado",
  "triggers.leads_added": "Pegados capturados",
  "triggers.lost": "Negocio perdido",
  "triggers.movedToLost": "Marcado como perdido",
  "triggers.movedToWon": "Marcado como ganancia",
  "triggers.neighborhood": "Por barrio",
  "triggers.neverBought": "Desconectado",
  "triggers.neverBoughtAProduct": "Nunca compré el producto",
  "triggers.neverConverted": "Nunca convertido",
  "triggers.notDealExistActive": "No hay negocio activo",
  "triggers.openedAnEmail": "Abrió el correo electrónico",
  "triggers.played": "Reproducción",
  "triggers.progressed": "Progresión del embudo",
  "triggers.progressedInFunnel": "Progresado en el embudo",
  "triggers.range": "Establecer el intervalo",
  "triggers.receivedAMessage": "Con cada mensaje recibido",
  "triggers.receivedAnEmail": "Recibió el correo electrónico",
  "triggers.regressed": "Regresión del embudo",
  "triggers.removed": "Remoto",
  "triggers.removed_from_participants": "Eliminado de los participantes",
  "triggers.removed_from_segmentation": "Eliminado de la segmentación",
  "triggers.removed_from_store": "Eliminado de la tienda",
  "triggers.removedFromarticipants": "Fue eliminado de los participantes",
  "triggers.removedFromSegmentation": "Fue eliminado de la segmentación",
  "triggers.removedFromStore": "Fue retirado de la tienda",
  "triggers.stage": "Está en el escenario",
  "triggers.stageIn": "Entrada en el paso",
  "triggers.stageInFunnel": "Se unió al escenario",
  "triggers.stageOut": "Salida",
  "triggers.stageOutFunnel": "Dejó el escenario",
  "triggers.state": "Por estado",
  "triggers.submited": "Formulario enviado",
  "triggers.tag_added": "Etiqueta agregada",
  "triggers.tag_removed": "Etiqueta eliminada",
  "triggers.tagAdded": "Se agregó la etiqueta",
  "triggers.tagHasNot": "No tiene la etiqueta",
  "triggers.tagHasTag": "Tiene la etiqueta",
  "triggers.tagRemoved": "La etiqueta fue eliminada",
  "triggers.tasklistCompleted": "Completó la lista de tareas",
  "triggers.ticketCancelled": "Boleto cancelado",
  "triggers.ticketGenerated": "Boleto generado",
  "triggers.tickets_canceled": "Cancelado",
  "triggers.triggerCol": "Grupo de condiciones",
  "triggers.unconfirmed_participant": "Participación sospechosa",
  "triggers.unconfirmedParticipation": "Participación desconfiada",
  "triggers.undidCheckin": "El check-in fue cancelado",
  "triggers.unload": "Salida de la página",
  "triggers.updated": "Actualizado",
  "triggers.updated_via_integration": "Actualizado a través de la integración",
  "triggers.updatedViaIntegration": "Se actualizó a través de la integración",
  "triggers.viewed": "Visa",
  "triggers.visited": "Visita",
  "triggers.won": "Ganancias comerciales",
  "videos.customVideoUrl": "URL de video",
  "videos.dailymotionVideo": "Video de Dailymotion",
  "videos.externalVideo": "URL de video alojado",
  "videos.facebookVideo": "Video de Facebook",
  "videos.fileVideo": "Archivo de video",
  "videos.twitchVideo": "Video de Twitch",
  "videos.videoProvider": "Proveedor de videos",
  "videos.vimeoVideo": "Video de Vimeo",
  "videos.youtubeVideo": "Video de YouTube",
  "whatsapp.authenticated": "Autenticado",
  "whatsapp.connected": "Conectado",
  "whatsapp.connectedAs": "Conectado como",
  "whatsapp.connectQiplus": "Conecte Qiplus con su cuenta de WhatsApp",
  "whatsapp.createNewAccount": "Crear una cuenta nueva",
  "whatsapp.deleted": "Mensaje eliminado",
  "whatsapp.edited": "Editado",
  "whatsapp.fullMobileNumber": "Número móvil con código de país y DDD",
  "whatsapp.generatingQrCode": "Generación del código QR ...",
  "whatsapp.inputMessage.placeholder": "Ingrese un mensaje",
  "whatsapp.inputMessage.tooltip":
    "Si lo desea, use [Ctrl + Enter] para enviar.",
  "whatsapp.me": "I",
  "whatsapp.message.send.fail": "No enviar.",
  "whatsapp.message.send.resend":
    "Haga clic para intentar reenviar este mensaje.",
  "whatsapp.message.send.resend.fail":
    "Falla al intentar volver a enviar un mensaje.",
  "whatsapp.send": "Para enviar",
  "whatsapp.settingYourAccount": "Preparando su cuenta ...",
  "whatsapp.successfullyConnectedQiplus":
    "¡Felicidades!Qiplus está conectado a su cuenta de WhatsApp",
  "whatsapp.waitingForQrCodeScan":
    "Abra WhatsApp en su teléfono y escanee el código QR para comenzar la sesión",
  "widgets.aboutUs": "Sobre nosotros",
  "widgets.AcceptorrRejectWithin": "Aceptar o rechazar dentro",
  "widgets.action": "Acción",
  "widgets.ActionsBuilder": "Seleccione las acciones",
  "widgets.ActionsSelector": "Seleccionar",
  "widgets.activeUsers": "Usuarios activos",
  "widgets.activitis": "Actividades",
  "widgets.activity": "Actividad",
  "widgets.activityBoard": "Tabla de actividades",
  "widgets.additionalContent": "Contenido adicional",
  "widgets.addNew": "Para crear",
  "widgets.addToCart": "Agregar al orden",
  "widgets.admin": "Administración",
  "widgets.adminTheme": "Tema administrativo",
  "widgets.advanced": "Avanzado",
  "widgets.advancedGridLists": "Listas de cuadrícula avanzadas",
  "widgets.advancedSearch": "Investigación avanzada",
  "widgets.agenda": "Orden del día",
  "widgets.alertDialog": "Diálogo de alerta",
  "widgets.alertDismiss": "Alerta de disgusto",
  "widgets.alertsWithIcons": "Alertas con íconos",
  "widgets.alertsWithLink": "Alertas con el enlace",
  "widgets.all": "Todo",
  "widgets.alreadyHavingAccountLogin": "Iniciar sesión de la cuenta ya haing",
  "widgets.anchorPlayGround": "Play Ground de anclaje",
  "widgets.animatedSlideDialogs": "Diálogo de diapositivas animadas",
  "widgets.anotherLink": "Otro enlace",
  "widgets.api": "API",
  "widgets.app": "Aplicación",
  "widgets.appBarsWithButtons": "Barras con aplicación de botones",
  "widgets.appearOrder": "Orden de apelación",
  "widgets.apply": "Aplicar",
  "widgets.appNotifications": "Notificaciones de la aplicación",
  "widgets.approve": "Aprobado",
  "widgets.areaChart": "Gráfico de área",
  "widgets.assignTeam": "Equipo designado",
  "widgets.author": "Editor",
  "widgets.autoAssignTeam": "Designar equipo automáticamente",
  "widgets.autoComplete": "Auto completo",
  "widgets.backend": "Backend",
  "widgets.backgroundVarient": "Variante de fondo",
  "widgets.badgeLinks": "Enlaces de insignia",
  "widgets.badgeWithHeadings": "Insignia con encabezados",
  "widgets.bandwidthUse": "Uso de ancho de banda",
  "widgets.barChart": "Gráfico de barras",
  "widgets.baseConfig": "Base de configuración",
  "widgets.basic": "Básico",
  "widgets.basicAlert": "Alerta básica",
  "widgets.basicCalendar": "Calendario básico",
  "widgets.basicCalender": "Calendario básico",
  "widgets.basicTab": "Pestaña básica",
  "widgets.basicTable": "Mesa básica",
  "widgets.booking": "Reserva",
  "widgets.bounced": "Rebotado",
  "widgets.browse": "Navegar",
  "widgets.bubbleChart": "Gráfico de burbujas",
  "widgets.buffer": "Buffer",
  "widgets.buttonNavigation": "Navegación de botones",
  "widgets.buttonNavigationWithNoLabel": "Navegación de botones sin etiqueta",
  "widgets.buttonOutline": "Contorno del botón",
  "widgets.buttonSize": "Tamaño del botón",
  "widgets.buttonState": "Estado de botón",
  "widgets.buttonWithIconAndLabel": "Botón con icono y etiqueta",
  "widgets.buyMore": "Seguir comprando",
  "widgets.byDay": "Por día",
  "widgets.byMonth": "Por mes",
  "widgets.byWeek": "Por semana",
  "widgets.byYear": "Por año",
  "widgets.campaignPerformance": "Desempeño de la campaña",
  "widgets.cancelled": "Cancelado",
  "widgets.cardGroup": "Grupo",
  "widgets.cardLink": "Enlace de la tarjeta",
  "widgets.cardOutline": "Contorno de la tarjeta",
  "widgets.cardSubtitle": "tarjeta subtitle",
  "widgets.cardTitle": "Título de tarjeta",
  "widgets.category": "Categoría",
  "widgets.centeredLabels": "Etiquetas centradas",
  "widgets.centeredTabs": "Pestañas centradas",
  "widgets.change": "Cambiar",
  "widgets.changeTransition": "Cambiar transición",
  "widgets.checkboxListControl":
    "Control de la lista de la casilla de verificación",
  "widgets.checklist": "Lista de verificación",
  "widgets.checklistQuestionary": "Cuestionario de la lista de verificación",
  "widgets.chipArray": "Chip de matriz",
  "widgets.chipWithAvatar": "Chip con avatar",
  "widgets.chipWithClickEvent": "Chip con el evento de clics",
  "widgets.chipWithIconAvatar": "Chip con Avatar de icono",
  "widgets.chipWithTextAvatar": "Chip con avatar de texto",
  "widgets.circularProgressBottomStart": "Inicio inferior de progreso",
  "widgets.code": "Código",
  "widgets.color": "Color",
  "widgets.commments": "Comentario",
  "widgets.company": "Empresa",
  "widgets.companyName": "Empresa",
  "widgets.comparePlans": "Compare nuestros planes",
  "widgets.components": "Componentes",
  "widgets.componet": "Componentes",
  "widgets.ComposeEmail": "Correo electrónico",
  "widgets.composeMail": "Nuevo correo electrónico",
  "widgets.conference": "Conferencia",
  "widgets.confirmationDialogs": "Diálogo de confirmación",
  "widgets.confirmed": "Confirmado",
  "widgets.contextualColoredTable": "Mesa de color contextual",
  "widgets.contexualAlerts": "Alertas contextuales",
  "widgets.contexualColoredSnackbars":
    "Barras de bocadillos contextuales de color",
  "widgets.contexualColoredTable": "Mesa de color contextual",
  "widgets.contexualVariations": "Variaciones contenedoras",
  "widgets.controlledAccordion": "Acordeón controlado",
  "widgets.croppedImage": "Imagen recortada",
  "widgets.culturesCalendar": "Calendario de culturas",
  "widgets.culturesCalender": "Calendario de culturas",
  "widgets.currentDate": "Fecha actual",
  "widgets.currentTime": "Actual",
  "widgets.customCalender": "Calendario Personalizado",
  "widgets.customClickableChip": "Chip de clickble personalizado",
  "widgets.customColor": "Color personalizado",
  "widgets.customColorCheckbox":
    "Casilla de verificación de color personalizado",
  "widgets.customControlBar": "Barra de control personalizada",
  "widgets.customDeleteIconChip": "Chip de icono de eliminación personalizado",
  "widgets.customIconAlert": "Alerta de icono personalizado",
  "widgets.customPicker": "Seleccionador personalizado",
  "widgets.customRendering": "Representación personalizada",
  "widgets.customStyleAlert": "Alerta de estilo personalizado",
  "widgets.daily": "A diario",
  "widgets.dailySales": "Ventas diarias",
  "widgets.dataTable": "Tabla de fecha",
  "widgets.dataUse": "Uso de datos",
  "widgets.date": "Fecha",
  "widgets.dateAndTimePicker": "Seleccionador de fecha y hora",
  "widgets.dateCreated": "Fecha de creación",
  "widgets.dateModified": "Fecha de modificación",
  "widgets.dates": "Fechas",
  "widgets.days_28": "28 días",
  "widgets.deadline": "Fecha límite",
  "widgets.defaultDatePicker": "Seleccionador de fecha predeterminado",
  "widgets.defaultPicker": "Seleccionador predeterminado",
  "widgets.defualtReactForm": "Formulario React predeterminado",
  "widgets.deletableChip": "Chip deletable",
  "widgets.deleted": "Eliminado",
  "widgets.description": "Descripción",
  "widgets.descriptionAlert": "Alerta de descripción",
  "widgets.designation": "Designación",
  "widgets.determinate": "Determinado",
  "widgets.dialogs": "Diálogo",
  "widgets.disableChip": "Deshabilitar el chip",
  "widgets.disabledCheckbox": "Casilla de verificación deshabilitada",
  "widgets.disabledRadio": "Radio discapacitado",
  "widgets.discoverPeople": "Descubre gente",
  "widgets.done": "Finalizado",
  "widgets.doughnut": "Rosquilla",
  "widgets.downshiftAutoComplete": "Downchift Auto completo",
  "widgets.dragAndDropCalendar": "Arrastrar y soltar el calendario",
  "widgets.dragAndDropCalender": "Arrastrar y soltar el calendario",
  "widgets.dragula": "Dragula",
  "widgets.emailsStatistics": "Estadísticas de correo electrónico",
  "widgets.employeeList": "Lista de empleados",
  "widgets.endDate": "Fin",
  "widgets.enterpriseEdition": "Edición empresarial",
  "widgets.enterYourPassword": "Ingrese su contraseña",
  "widgets.expenseCategory": "Categoría de gastos",
  "widgets.expenses": "Exponiciones",
  "widgets.exportToExcel": "Exportar a Excel",
  "widgets.externalUrl": "URL externa",
  "widgets.faq(s)": "Preguntas frecuentes",
  "widgets.file": "Archivo",
  "widgets.filters": "Filtros",
  "widgets.fixedTabs": "Pestañas fijas",
  "widgets.flatButtons": "Botones planos",
  "widgets.floatingActionButtons": "Botones de acción flotante",
  "widgets.folderLists": "Listas de carpetas",
  "widgets.follow": "seguir",
  "widgets.follower": "Seguidor",
  "widgets.following": "Siguiente",
  "widgets.forcedScrolledButtons": "Botones de desplazamiento forzado",
  "widgets.forgetPassword": "Olvídate de la contraseña",
  "widgets.formattedInputs": "Entradas formateadas",
  "widgets.formDialogs": "Diálogos de formulario",
  "widgets.formGrid": "Formar cuadrícula",
  "widgets.formValidate": "Formulario Validar",
  "widgets.formValidation": "Validación de formulario",
  "widgets.free": "Gratis",
  "widgets.frequentlyAskedQuestions": "Preguntas frecuentes",
  "widgets.frontend": "Borde",
  "widgets.fullDescription": "Descripción completa",
  "widgets.fullScreenDialogs": "Diálogo de pantalla completa",
  "widgets.gallery": "Galería",
  "widgets.global": "Global",
  "widgets.helpButtonTooltip": "Ayuda",
  "widgets.helpToShareText":
    "¡Ayúdanos a difundir el mundo compartiendo nuestro sitio web con tus amigos y seguidores en las redes sociales!",
  "widgets.hiddenLabels": "Etiquetas ocultas",
  "widgets.high": "Alto",
  "widgets.horizontalBar": "Barra fija",
  "widgets.horizontalLinear": "Horizontal lineal",
  "widgets.horizontalLinearAlternativeLabel":
    "Etiqueta alternativa lineal horizontal",
  "widgets.horizontalLinerAlternativeLabel":
    "Etiqueta alternativa de revestimiento horizontal",
  "widgets.horizontalNonLinear": "No lineal",
  "widgets.horizontalNonLinearAlternativeLabel":
    "Etiqueta alternativa no lineal horizontal",
  "widgets.horizontalNonLinerAlternativeLabel":
    "Etiqueta alternativa no delineador horizontal",
  "widgets.horizontalStyleCheckbox":
    "Casilla de verificación de estilo horizontal",
  "widgets.howWouldYouRateUs": "¿Cómo nos calificará?",
  "widgets.httpLiveStreaming": "Transmisión en vivo http",
  "widgets.iconButton": "Botón de icono",
  "widgets.iconNavigation": "Navegación ícono",
  "widgets.iconsAvatars": "Íconos de avatares",
  "widgets.iconsTabs": "Pestañas de icono",
  "widgets.iconWithLabel": "Icono con etiqueta",
  "widgets.imageAvatars": "Avatares de imagen",
  "widgets.imageOnlyGridLists": "Listas de cuadrícula de solo imagen",
  "widgets.important": "Importante",
  "widgets.indeterminate": "Indeterminado",
  "widgets.inlineForm": "Forma en línea",
  "widgets.inputGridSizing": "Dimensionamiento de la cuadrícula de entrada",
  "widgets.inputSizing": "Dimensionamiento de entrada",
  "widgets.inputWithDanger": "Entrada con peligro",
  "widgets.inputWithSuccess": "Entrada con la consecuencia",
  "widgets.insetDividers": "Divisores insertados",
  "widgets.insetLists": "Listas de inserción",
  "widgets.interactiveIntegration": "Integración interactiva",
  "widgets.InteractiveLists": "Listas interactivas",
  "widgets.interminateSelection": "Interminar la selección",
  "widgets.issue": "Asunto",
  "widgets.keyboardShortcuts": "Atajos de teclado",
  "widgets.lastMonth": "Mes pasado",
  "widgets.lastWeek": "La semana pasada",
  "widgets.latestPost": "Última publicación",
  "widgets.layouts": "Diseños",
  "widgets.lettersAvatars": "Avatares de letras",
  "widgets.lifetime": "Vida",
  "widgets.linearProgressLineBar": "Barra de línea de progreso lineal",
  "widgets.lineBarAreaChart": "Gráfico de área de barra de línea",
  "widgets.lineChart": "Gráfico de línea",
  "widgets.listDividers": "Lista de divisores",
  "widgets.listing": "Listado",
  "widgets.listItemWithImage": "Lista del elemento con imagen",
  "widgets.LiveChatSupport": "Soporte de chat en vivo",
  "widgets.lockScreen": "Pantalla de bloqueo",
  "widgets.logIn": "Acceso",
  "widgets.logOut": "Salir",
  "widgets.logs": "Registro",
  "widgets.low": "Bajo",
  "widgets.mail": "Correo electrónico",
  "widgets.mailing.grapesjs.copy":
    'Variable "[%val]" copiada al área de transferencia.Use "Ctrl+V" para pegar',
  "widgets.master": "Maestro",
  "widgets.materialBadge": "Material de insignia",
  "widgets.maxHeightMenu": "Menú de altura máxima",
  "widgets.Mega": "Mega",
  "widgets.message": "Mensaje",
  "widgets.messages": "Mensajes",
  "widgets.ModulesPostsSelector": "Seleccionar",
  "widgets.monthly": "Mensual",
  "widgets.multilevel": "Multinivel",
  "widgets.multipleTabs": "Múltiples pestañas",
  "widgets.multiSelectList": "Lista múltiple de selección",
  "widgets.MutltiSelectList": "Lista múltiple de selección",
  "widgets.myProfile": "Mi perfil",
  "widgets.nativeSelect": "Seleccionar nativo",
  "widgets.nestedLists": "Listas de NASD",
  "widgets.netProfit": "Beneficio neto",
  "widgets.new": "Nuevo",
  "widgets.new.fem": "Nuevo",
  "widgets.new.plural": "Nuevo",
  "widgets.new.plural.fem": "Nuevo",
  "widgets.newCustomers": "Nuevos clientes",
  "widgets.newEmails": "Nuevos correos electrónicos",
  "widgets.noLogsYet": "No hay registros por ahora",
  "widgets.note": "Nota",
  "widgets.notifications": "Notificaciones",
  "widgets.number": "Número",
  "widgets.occupation": "Profesión",
  "widgets.OngoingProjects": "Proyectos en progreso",
  "widgets.onlineSources": "Fuentes en línea",
  "widgets.onlineVistors": "Visitantes",
  "widgets.open": "Abierto",
  "widgets.openAlertDialog": "Diálogo de alerta abierta",
  "widgets.openBottom": "Fondo abierto",
  "widgets.openFormDialog": "Diálogo de formulario abierto",
  "widgets.openLeft": "A la izquierda abierta",
  "widgets.openResponsiveDialog": "Abrir diálogo de respuesta receptiva",
  "widgets.openRight": "Abrir a la derecha",
  "widgets.openSimpleDialog": "Abrir diálogo simple",
  "widgets.openTop": "Top abierto",
  "widgets.optionA": "Opción A",
  "widgets.optionB": "Opción B",
  "widgets.optionC": "Opción C",
  "widgets.optionM": "Opción M",
  "widgets.optionN": "Opción n",
  "widgets.optionO": "Opción O",
  "widgets.orderDate": "Fecha de solicitud",
  "widgets.orders": "Solicitudes",
  "widgets.orderStatus": "Estado de pedido",
  "widgets.ourLocations": "Nuestras ubicaciones",
  "widgets.ourMissions": "Nuestra misión",
  "widgets.ourMotivation": "Nuestra motivación",
  "widgets.ourVission": "Nuestra visión",
  "widgets.outlineChip": "Contorno de chip",
  "widgets.overallTrafficStatus": "Tráfico",
  "widgets.overlayCard": "Tarjeta de superposición",
  "widgets.paid": "Pagado",
  "widgets.paper": "Papel",
  "widgets.password": "Contraseña",
  "widgets.passwordPromptAlert": "ALERTA DE ADRICIÓN DE PASSACIÓN",
  "widgets.pending": "Pendiente",
  "widgets.permanentdrawer": "Cajón permanente",
  "widgets.permanentDrawers": "Cajones permanentes",
  "widgets.persistentdrawer": "Cajón persistente",
  "widgets.personalDetails": "Detalles personales",
  "widgets.personalEdition": "Edición personal",
  "widgets.personalSchedule": "Mi agenda",
  "widgets.phoneNo": "Teléfono",
  "widgets.pieChart": "Gráfico",
  "widgets.pinedSubHeader": "Encabezado de subsidio",
  "widgets.pinnedSubheaderList": "Lista de subtítulo cubierto",
  "widgets.pixelScript": "Script de píxel de qiplus",
  "widgets.plan": "Departamento",
  "widgets.plans": "Planes",
  "widgets.polarChart": "Gráfico polar",
  "widgets.positionedSnackbar": "Snackbar posicionado",
  "widgets.positionedToolTips": "Snackbar posicionado",
  "widgets.PostsSelector": "Seleccionar",
  "widgets.preventScrolledButtons": "Evitar botones desplazados",
  "widgets.preview": "Avance",
  "widgets.previousChat": "Chat anterior",
  "widgets.price": "Precio",
  "widgets.pricing": "Precios",
  "widgets.primary": "Primario",
  "widgets.private": "Privado",
  "widgets.pro": "Pro",
  "widgets.productReports": "Informes de productos",
  "widgets.productsReports": "Informes de productos",
  "widgets.productStats": "Estadística de productos",
  "widgets.professional": "Profesional",
  "widgets.professionals": "Profesionales",
  "widgets.profile": "Perfil",
  "widgets.projectManagement": "Gestión de proyectos",
  "widgets.ProjectStatus": "Estado del proyecto",
  "widgets.projectTaskManagement": "Gestión de tareas del proyecto",
  "widgets.promptAlert": "ALERTA DE ALTACIÓN ANTERIOR",
  "widgets.qiplusPlan": "Plan Qiplus",
  "widgets.qiplusUrl": "URL QIPLUS",
  "widgets.query": "Consulta",
  "widgets.QuickLinks": "Enlaces rápidos",
  "widgets.quillEditor": "Pluma editor",
  "widgets.quoteOfTheDay": "Cita del día",
  "widgets.radarChart": "Gráfico de radar",
  "widgets.radioButtons": "Botones de radio",
  "widgets.raisedButton": "Botón elevado",
  "widgets.ratings": "Calificaciones",
  "widgets.reactAutoSuggest": "React AutoSugest",
  "widgets.reactAutoSuggests": "React Auto sugiere",
  "widgets.reactButton": "Botón React",
  "widgets.reactDND": "Reaccionar DND",
  "widgets.reactGridControlledStateMode":
    "Modo de estado controlado de reacción en la cuadrícula",
  "widgets.reactSelect": "Reaccionar seleccione",
  "widgets.recentActivities": "Actividad reciente",
  "widgets.recentChat": "Chat reciente",
  "widgets.recentNotifications": "Notificaciones recientes",
  "widgets.recentOrders": "Últimas solicitudes",
  "widgets.RecentOrders": "Últimas solicitudes",
  "widgets.recents": "Frases",
  "widgets.refunded": "Reembolsado",
  "widgets.reject": "Desechar",
  "widgets.responsiveFlipTable": "Mesa de chanclas receptiva",
  "widgets.responsiveFullScreen": "Pantalla completa receptiva",
  "widgets.responsiveTable": "Tabla receptiva",
  "widgets.sales": "Ventas",
  "widgets.saveAsDrafts": "Guardar los borradores",
  "widgets.search": "Buscar",
  "widgets.searchIdeas": "Encontrar ideas",
  "widgets.searchInMailbox": "Buscar correos electrónicos",
  "widgets.searchMailList": "Buscar correos electrónicos",
  "widgets.secondaryHeadingAndColumns": "Encabezado secundario y columnas",
  "widgets.selectableCalender": "Calendario seleccionable",
  "widgets.selectADefaultAddress": "Seleccione una dirección predeterminada",
  "widgets.selectedMenu": "Menú seleccionado",
  "widgets.selectMultiple": "Seleccionar múltiples",
  "widgets.selectProject": "Seleccionar proyecto",
  "widgets.selectTripDestination": "Seleccionar destino de viaje",
  "widgets.send": "Para enviar",
  "widgets.ShareWithFriends": "¡Compartir!",
  "widgets.shipTo": "Entregar",
  "widgets.shortDescription": "Descripción breve",
  "widgets.signIn": "Acceso",
  "widgets.signUp": "Registro",
  "widgets.Simple App Bars": "Barras de aplicaciones simples",
  "widgets.simpleAppBar": "Barra de aplicación simple",
  "widgets.simpleCards": "Tarjetas simples",
  "widgets.simpleCheckbox": "Casilla de verificación simple",
  "widgets.simpleDialogs": "Diálogo simples",
  "widgets.simpleExpansionPanel": "Panel de expansión simple",
  "widgets.simpleLists": "Listas simples",
  "widgets.simpleMenus": "Menús simples",
  "widgets.simpleSelect": "Seleccionar simple",
  "widgets.simpleSnackbar": "Snackbar simple",
  "widgets.simpleTextField": "Campo de texto simple",
  "widgets.singleLineGridLists": "Listas de cuadrícula de una sola línea",
  "widgets.singleLineItem": "Línea de una sola línea",
  "widgets.siteVisitors": "Visitantes",
  "widgets.social": "Social",
  "widgets.socialCompanines": "Empresas sociales",
  "widgets.socialMediaButton": "Botón de redes sociales",
  "widgets.socialNewtork": "Red social",
  "widgets.socialNewtorks": "Redes sociales",
  "widgets.spam": "Correo basura",
  "widgets.speacialTitleTreatment": "Tratamiento especial de título",
  "widgets.stackedAreaChart": "Gráfico de área apilada",
  "widgets.stackedBarChart": "Gráfico de barra apilada",
  "widgets.standard": "Estándar",
  "widgets.starred": "Sembrado de estrellas",
  "widgets.startDate": "Comenzar",
  "widgets.startToBasic": "Básico",
  "widgets.status": "Estado",
  "widgets.stepper": "Agitador",
  "widgets.stockExchange": "Bolsa",
  "widgets.styles": "Estilos",
  "widgets.subject": "Sujeto",
  "widgets.successAlert": "Operación realizada con éxito",
  "widgets.support": "Apoyo",
  "widgets.supportRequest": "Solicitud de soporte",
  "widgets.swiches": "Swiches",
  "widgets.switches": "Swiches",
  "widgets.switchLists": "Cambiar listas",
  "widgets.tabs": "Cortina a la italiana",
  "widgets.target": "Objetivo",
  "widgets.taskList": "Tareas",
  "widgets.tax": "Impuesto",
  "widgets.team": "Equipo",
  "widgets.teamEdition": "Edición de equipo",
  "widgets.temporaryDrawers": "Cajones de temporismo",
  "widgets.text": "Texto",
  "widgets.textArea": "Área de texto",
  "widgets.thisWeek": "Esta semana",
  "widgets.time": "Tiempo",
  "widgets.timePicker": "Seleccionador de tiempo",
  "widgets.to": "A",
  "widgets.today": "Hoy",
  "widgets.todayOrders": "Solicitudes de día",
  "widgets.toDoList": "Tareas",
  "widgets.tooltip": "Comedia de herramientas",
  "widgets.topSellings": "Más vendidas",
  "widgets.total": "Total",
  "widgets.total_over_range": "Total por encima de la pista",
  "widgets.totalActiveUsers": "Usuarios activos totales",
  "widgets.totalOrders": "Solicitudes totales",
  "widgets.totalRequest": "Solicitud total",
  "widgets.totalRevenue": "Ingresos totales",
  "widgets.totalSales": "Ventas totales",
  "widgets.totalVisitors": "Visitantes totales",
  "widgets.trackingNumber": "El número de rastreo",
  "widgets.trafficChannel": "Canal de tráfico",
  "widgets.trafficSources": "Fuente de tráfico",
  "widgets.transactionList": "Lista de transacciones",
  "widgets.transferReport": "Informe de transferencia",
  "widgets.transitionControlDirection": "Dirección de control de transición",
  "widgets.TriggersBuilder": "Seleccione los desencadenantes",
  "widgets.tutorials": "Tutoriales",
  "widgets.tweets": "Tweets",
  "widgets.typeYourQuestions": "Escriba sus preguntas",
  "widgets.uncontrolledDisableAlerts":
    "Alertas de desactivación no controladas",
  "widgets.unitPrice": "Precio unitario",
  "widgets.unset": "No estructurado",
  "widgets.unsubscribe": "Umubscribe",
  "widgets.upcomingEvents": "Siguientes eventos",
  "widgets.updated10Minago": "Actualizado 10 min",
  "widgets.updateProfile": "ACTUALIZAR PERFIL",
  "widgets.updateYourEmailAddress":
    "Actualice su dirección de correo electrónico",
  "widgets.upgrade": "mejora",
  "widgets.upgradePlan": "Cambiar mi plan",
  "widgets.upgradeToAdvance": "Actualizar para avanzar",
  "widgets.upgradeToEnableAction": "Actualizar para habilitar esta acción",
  "widgets.upgradeToPro": "Actualizar a Pro",
  "widgets.url": "Url",
  "widgets.user": "Usuario",
  "widgets.username": "Usuario",
  "widgets.usersList": "Lista de usuarios",
  "widgets.verticalChart": "Gráfico vertical",
  "widgets.verticalStepper": "Paso a paso vertical",
  "widgets.VerticalStyleCheckbox": "Casilla de verificación de estilo vertical",
  "widgets.visitors": "Visitantes",
  "widgets.volume": "Volumen",
  "widgets.warningAlert": "Alerta de advertencia",
  "widgets.weekly": "Semanalmente",
  "widgets.weekPicker": "Seleccionador de la semana",
  "widgets.widgets": "Widgets",
  "widgets.withDisableTabs": "Con pestañas de desactivación",
  "widgets.withDownloadButton": "Con botón de descarga",
  "widgets.withError": "Con error",
  "widgets.withHtmlAlert": "Con alerta HTML",
  "widgets.wordpressTheme": "Tema de WordPress",
  "widgets.workWeek": "Semana laboral",
  "widgets.wrappedLabels": "Etiquetas envueltas",
  "widgets.yearly": "Anual",
  "widgets.yesterday": "Ayer",
};


const pendingTranslationStrings = {
  'pending.translations': 'Should be here',
}

const moduleStrings = {}

Object.keys(AppModules).forEach(collection => {
  const thisModule = AppModules[collection]
  const menuGroup = thisModule.menu_group
  const collectionKeys = ['modules', 'sidebar', 'menu', 'triggers']

  let { label, singular, menu_label, gender, shortname } = thisModule
  const menulabel = menu_label || label

  collectionKeys.forEach(key => {
    const thisLabel = key === 'menu' ? menulabel : label
    const singularLabel = singular || thisLabel
    const shortLabel = shortname || singularLabel

    moduleStrings[`${key}.${collection}`] = thisLabel
    moduleStrings[`${key}.${collection}.singular`] = singularLabel
    moduleStrings[`${key}.${collection}.short`] = shortLabel

    moduleStrings[`${key}.${collection}.added`] = `${singularLabel} Criad[%gender]`.replace('[%gender]', gender === 'f' ? 'a' : 'o')
    moduleStrings[`${key}.${collection}.changed`] = `${singularLabel} Modificad[%gender]`.replace('[%gender]', gender === 'f' ? 'a' : 'o')
    moduleStrings[`${key}.${collection}.removed`] = `${singularLabel} Removid[%gender]`.replace('[%gender]', gender === 'f' ? 'a' : 'o')

    moduleStrings[`${key}.${collection}_added`] = moduleStrings[`${key}.${collection}.added`]
    moduleStrings[`${key}.${collection}_changed`] = moduleStrings[`${key}.${collection}.changed`]
    moduleStrings[`${key}.${collection}_removed`] = moduleStrings[`${key}.${collection}.removed`]
    if (menuGroup) {
      moduleStrings[`${key}.${menuGroup}Group`] = thisLabel

      if (!moduleStrings[`${key}.${menuGroup}`]) {
        moduleStrings[`${key}.${menuGroup}`] = thisLabel
        moduleStrings[`${key}.${menuGroup}.singular`] = singularLabel
        moduleStrings[`${key}.${menuGroup}.short`] = shortLabel
      }
    }
  })

  const editKeys = {
    view: 'See [%collection]',
    'view.singular': 'See [%singular]',
    new: 'New [%singular]',
    create: 'Create a [%singular]',
    add: 'Add [%singular]',
    createNew: 'Create new [%singular]',
    update: 'Update [%singular]',
    edit: 'Edit [%singular]',
    delete: 'Delete [%singular]',
    remove: 'Remove [%singular]',
    'clone.singular': 'Clone [%singular]',
    clone: 'Clone [%collection]',
    model: '[%singular] model',
    example: '[%singular] example',
    choose: 'Choose [%singular]',
    select: 'Select [%collection]',
    selected: '[%collection] selecteds',
    'selected.singular': '[%singular] selected',
    import: 'Import [%collection]',
    associate: 'Associate [%collection]',
    data: 'Data of [%singular]',
    confirmRemoval: 'Deseja eliminar [%gender] [%singular]?',
    confirmUpdate: 'Deseja atualizar [%gender] [%singular]?',
    willBeSentToTrash: '[%gender] [%singular] será enviad[%gender] para a lixeira',
    'willBeSentToTrash.plural': '[%gender] [%collection] serão enviad[%gender]s para a lixeira',
    willBeRemoved: '[%gender] [%singular] será eliminad[%gender]',
    'willBeRemoved.plural': '[%gender]s [%collection] serão eliminad[%gender]s',
    sentToTrash: '[%singular] enviad[%gender] para a lixeira',
    'sentToTrash.plural': '[%collection] enviad[%gender]s para a lixeira',
    removed: '[%gender] [%singular] será eliminad[%gender]',
    'removed.plural': '[%gender]s [%collection] serão eliminad[%gender]s',
    savedMsg: '[%singular] salv[%gender] com sucesso',
    deletedMsg: '[%singular] eliminad[%gender]',
    'deletedMsg.plural': '[%collection] eliminad[%gender]s',
    'deletedMsg.failure': '[%collection] não puderam ser eliminad[%gender]s',
    createdMsg: '[%singular] criad[%gender] com sucesso',
    'createdMsg.plural': '[%collection] criad[%gender]s com sucesso',
    'createdMsg.failure': '[%collection] não puderam ser criad[%gender]s',
    updatedMsg: '[%singular] atualizad[%gender]',
    'updatedMsg.plural': '[%collection] atualizad[%gender]s',
    'updatedMsg.failure': '[%collection] não puderam ser atualizad[%gender]s',
    inexistsMsg: '[%gender] [%singular] não existe no banco de dados',
    noPostsFound: 'Nenhum[%fem] [%singular] encontrad[%gender]',
    savePostBefore: 'Salve [%gender] [%singular] antes de continuar',
    leaveBlankForUnlimited: 'Deixe em branco para disponibilizar tod[%gender]s [%gender]s [%collection] da conta',
  }

  Object.keys(editKeys).forEach(key => {
    let msg = editKeys[key]

    // const fem = gender === 'f' ? 'a' : '';
    // const masc = gender === 'm' ? 'o' : '';
    // const genderVocal = gender === 'f' ? 'a' : 'o';
    const singularLabel = singular || label
    const shortLabel = shortname || singularLabel

    msg = msg.replace(/\[%collection\]/g, label)
    msg = msg.replace(/\[%singular\]/g, singularLabel)
    msg = msg.replace(/\[%shortname\]/g, shortLabel)
    // msg = msg.replace(/\[%gender\]/g, genderVocal);
    // msg = msg.replace(/\[%fem\]/g, fem);
    // msg = msg.replace(/\[%masc\]/g, masc);

    moduleStrings[`${collection}.${key}`] = msg
    moduleStrings[`${collection}.${key}.short`] = msg.replace(singularLabel, shortLabel)
  })
})

Object.keys(AppTaxonomies).forEach(taxName => {
  const thisTaxonomy = AppTaxonomies[taxName]
  const menuGroup = thisTaxonomy.menu_group
  const collectionKeys = ['taxonomies', 'sidebar', 'menu']

  let { label, singular, menu_label } = thisTaxonomy
  const menulabel = menu_label || label

  const editKeys = {
    view: 'Ver [%taxName]',
    'view.singular': 'Ver [%singular]',
    new: 'Nov[%gender] [%singular]',
    create: 'Create [%singular]',
    add: 'Adicionar [%singular]',
    createNew: 'Create nov[%gender] [%singular]',
    update: 'Atualizar [%singular]',
    edit: 'Editar [%singular]',
    delete: 'Eliminar [%singular]',
    remove: 'Excluir [%singular]',
    'clone.singular': 'Clonar [%singular]',
    clone: 'Clonar [%taxName]',
    model: 'Modelo de [%singular]',
    example: 'Exemplo de [%singular]',
    choose: 'Selecione um[%fem] [%taxName]',
    select: 'Selecionar [%taxName]',
    associate: 'Associar [%taxName]',
    data: 'Dados d[%gender] [%singular]',
    confirmRemoval: 'Deseja eliminar [%gender] [%singular]?',
    confirmUpdate: 'Deseja atualizar [%gender] [%singular]?',
    willBeSentToTrash: '[%gender] [%singular] será enviad[%gender] para a lixeira',
    willBeRemoved: '[%gender] [%singular] será eliminad[%gender]',
    deletedMsg: '[%singular] eliminad[%gender]',
    'deletedMsg.plural': '[%collection] eliminad[%gender]s',
    'deletedMsg.failure': '[%collection] não puderam ser eliminad[%gender]s',
    createdMsg: '[%singular] criad[%gender] com sucesso',
    'createdMsg.plural': '[%collection] criad[%gender]s com sucesso',
    'createdMsg.failure': '[%collection] não puderam ser criad[%gender]s',
    updatedMsg: '[%singular] atualizad[%gender]',
    'updatedMsg.plural': '[%collection] atualizad[%gender]s',
    'updatedMsg.failure': '[%collection] não puderam ser atualizad[%gender]s',
    inexistsMsg: '[%gender] [%singular] não existe no banco de dados',
    noPostsFound: 'Nenhum[%fem] [%singular] encontrad[%gender]',
  }

  thisTaxonomy.collections.forEach(collection => {
    const thisModule = AppModules[collection]

    collectionKeys.forEach(key => {
      const thisLabel = key === 'menu' ? menulabel : label
      const singularLabel = singular || thisLabel

      moduleStrings[`${key}.${taxName}.${collection}`] = thisLabel
      moduleStrings[`${key}.${taxName}.${collection}.singular`] = singularLabel

      if (menuGroup) {
        moduleStrings[`${key}.${menuGroup}Group`] = thisLabel

        if (!moduleStrings[`${key}.${menuGroup}`]) {
          moduleStrings[`${key}.${menuGroup}`] = thisLabel
          moduleStrings[`${key}.${menuGroup}.singular`] = singularLabel
        }
      }
    })

    Object.keys(editKeys).forEach(key => {
      let msg = editKeys[key]

      // const fem = gender === 'f' ? 'a' : '';
      // const masc = gender === 'm' ? 'o' : '';
      // const genderVocal = gender === 'f' ? 'a' : 'o';
      const singularLabel = singular || label

      msg = msg.replace(/\[%taxName\]/g, label)
      msg = msg.replace(/\[%collection\]/g, thisModule.label)
      msg = msg.replace(/\[%singular\]/g, singularLabel)
      // msg = msg.replace(/\[%gender\]/g, genderVocal);
      // msg = msg.replace(/\[%fem\]/g, fem);
      // msg = msg.replace(/\[%masc\]/g, masc);

      moduleStrings[`${taxName}.${collection}.${key}`] = msg

      if (!moduleStrings[`${taxName}.${key}`]) {
        moduleStrings[`${taxName}.${key}`] = msg
      }
    })
  })
})

const langMessages = {
  ...es_ES_Strings,
  ...moduleStrings,
  ...pendingTranslationStrings,
}
module.exports = langMessages
