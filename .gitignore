# See https://help.github.com/ignore-files/ for more about ignoring files.

# ----------------------
# QIPLUS
# ----------------------
credentials
dist.zip

# ----------------------
# FROM REACT
# https://github.com/facebook/react/blob/master/.gitignore
# ----------------------
docs
.DS_STORE
node_modules
scripts/flow/*/.flowconfig
*~
*.pyc
.grunt
_SpecRunner.html
__benchmarks__
build/
remote-repo/
coverage/
.module-cache
fixtures/dom/public/react-dom.js
fixtures/dom/public/react.js
test/the-files-to-test.generated.js
*.log*
chrome-user-data
*.sublime-project
*.sublime-workspace
.idea
*.iml
*.swp
*.swo
package-lock.json
tasks.md
packages/react-devtools-core/dist
packages/react-devtools-extensions/chrome/build
packages/react-devtools-extensions/chrome/*.crx
packages/react-devtools-extensions/chrome/*.pem
packages/react-devtools-extensions/firefox/build
packages/react-devtools-extensions/firefox/*.xpi
packages/react-devtools-extensions/firefox/*.pem
packages/react-devtools-extensions/shared/build
packages/react-devtools-inline/dist
packages/react-devtools-shell/dist



# ----------------------
# ORIGINAL FROM reactify
# ----------------------

# dependencies
/node_modules

# testing
/coverage

# production
/dist

# misc
.DS_Store
.env
.env.local
.env.development.local
.env.test.local
.env.staging
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*


*.todo
.aider*
